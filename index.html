<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - AI Prompt Management Tool</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/ai-components.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('index.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="index.html" style="color: #4a6cf7; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="hero">
        <div class="container">
            <h1>Welcome to Promz</h1>
            <h2>Your AI Prompt Management and Collaboration Platform</h2>
            <p>Organize, optimize, and collaborate on AI prompts with a powerful cross-platform tool.</p>
            <div class="cta-buttons">
                <a href="download.html" class="btn primary">Download Now</a>
                <a href="about.html" class="btn secondary">Learn More</a>
                <a href="releases/index.html" class="btn secondary">Release Notes</a>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2>Key Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>Prompt Management</h3>
                    <p>Organize and categorize your AI prompts for easy access and reuse.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>Real-time Collaboration</h3>
                    <p>Work together on prompts with your team in real-time with conflict resolution.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>Smart Suggestions</h3>
                    <p>Get intelligent suggestions for improving your prompts with entity detection.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>Cross-Platform</h3>
                    <p>Use Promz on all your devices with seamless cloud synchronization.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="download" class="download">
        <div class="container">
            <h2>Download Promz</h2>
            <p>Available for multiple platforms</p>
            <div class="download-options">
                <a href="download.html" class="download-button">
                    <div class="platform-icon">📱</div>
                    <div class="platform-info">
                        <h3>Android</h3>
                        <p>Get it on Google Play</p>
                    </div>
                </a>
                <a href="download.html" class="download-button">
                    <div class="platform-icon">🍎</div>
                    <div class="platform-info">
                        <h3>iOS</h3>
                        <p>Download on App Store</p>
                    </div>
                </a>
                <a href="download.html" class="download-button">
                    <div class="platform-icon">💻</div>
                    <div class="platform-info">
                        <h3>Desktop</h3>
                        <p>Windows, macOS, Linux</p>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script src="js/main.js"></script>
    <script src="js/ai-suggestions.js"></script>
    <script src="js/workflow-automation.js"></script>
    <script src="js/ai-feedback-system.js"></script>
</body>

</html>