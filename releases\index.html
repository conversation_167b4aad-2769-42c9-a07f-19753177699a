<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Release Notes</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/ai-components.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('../includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the header for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="../beta/index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="../feedback/index.html"');

                document.getElementById('header-placeholder').innerHTML = data;

                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                    if (link.href.includes('releases/index.html') || link.textContent.trim() === 'Release Notes') {
                        link.classList.add('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="../index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="../index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="../about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="../download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="../beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="../feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="content">
        <div class="container">
            <h1>Release Notes</h1>
            
            <div class="release-section content-section">
                <h2>Version 1.0.23 (Build 24)</h2>
                <h3>New Features</h3>
                <ul>
                    <li>Recent prompts - Users can access a list of their recent prompts just like the list of popular prompts</li>
                </ul>
                
                <h3>Improvements</h3>
                <ul>
                    <li>Prevent auto-logouts after extended inactivity</li>
                    <li>Provide visual cues such as loading/ progress indicators when processing requests and loading data and responses</li>
                    <li>Show key information related to running and completed tasks on the task list so that users can distinguish, for instance, between two different executions of the same prompt with varying input parameters</li>
                    <li>Reset or update session data when the token expires and the user signs out</li>
                </ul>
            </div>

            <div class="release-section content-section">
                <h2>Version 1.0.12 (Build 13)</h2>
                <h3>New Features</h3>
                <ul>
                    <li>Smart prompt suggestions and optimization</li>
                    <li>Popular prompts - Users can access their frequently used prompts straight from their home page</li>
                    <li>Support for custom prompts</li>
                    <li>AutoMagic prompt improvement</li>
                </ul>
                
                <h3>Improvements</h3>
                <ul>
                    <li>Performance improvements to improve startup time and execution time</li>
                    <li>The issue with the prompt outputs being cut off midway has been fixed. If you happen to run into this issue, please let us know by using our <a href="../feedback/index.html">feedback form</a></li>
                    <li>Improved layout and navigation, regardless of the number of prompts in users' prompt collection</li>
                    <li>Help/About section - A detailed Help/About section has been added to the app with app usage guidance</li>
                    <li>Issue with auto-logouts after extended inactivity has been fixed. If you happen to run into this issue, please let us know by using our <a href="../feedback/index.html">feedback form</a></li>
                    <li>Privacy section has been added addressing data storage, retention and privacy concerns</li>
                    <li>Website reorganization and enhanced feedback functionality</li>
                </ul>
            </div>

        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('../includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the footer for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="../beta/index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="../feedback/index.html"');
                data = data.replace(/href="terms\.html"/g, 'href="../terms.html"');
                data = data.replace(/href="privacy\.html"/g, 'href="../privacy.html"');

                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="../about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="../download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="../beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="../feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="../privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script src="../js/main.js"></script>
    <script src="../js/ai-suggestions.js"></script>
    <script src="../js/workflow-automation.js"></script>
    <script src="../js/ai-feedback-system.js"></script>
</body>
</html>
