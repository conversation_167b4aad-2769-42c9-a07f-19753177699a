<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Redirecting...</title>
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4a6cf7;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        p {
            color: #666;
            max-width: 600px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="loader"></div>
    <h1>Redirecting to Promz</h1>
    <p>Please wait while we redirect you to the appropriate destination...</p>
    <p id="fallback-message" style="display: none;">
        If you're not redirected automatically,
        <a href="download.html" id="fallback-link">click here</a>.
    </p>

    <div id="contact-info" style="margin-top: 30px; padding: 20px; background-color: rgba(255,255,255,0.1); border-radius: 8px; display: none;">
        <h3 style="color: #333; margin-bottom: 10px;">Need Help?</h3>
        <p style="color: #666; margin: 5px 0;">Email: <a href="mailto:<EMAIL>" style="color: #4a6cf7;"><EMAIL></a></p>
        <p style="color: #666; margin: 5px 0;">General Info: <a href="mailto:<EMAIL>" style="color: #4a6cf7;"><EMAIL></a></p>
    </div>
    
    <script src="js/deeplink-handler.js"></script>
    <script>
        // Show fallback message after 3 seconds
        setTimeout(function() {
            document.getElementById('fallback-message').style.display = 'block';
            document.getElementById('contact-info').style.display = 'block';
            
            // Update the fallback link with the ID if available
            const path = window.location.pathname;
            const shortId = path.startsWith('/p/') ? path.substring(3) : path.replace(/^\/+/, '');
            if (shortId) {
                document.getElementById('fallback-link').href = `download.html?id=${shortId}`;
            }
        }, 3000);
    </script>

    <script src="js/main.js"></script>
</body>
</html>
