/**
 * AI Feedback Collection and Analysis System
 * Captures user feedback with metadata for model improvement
 */

class AIFeedbackSystem {
    constructor() {
        this.feedbackQueue = [];
        this.sessionData = {
            sessionId: this.generateSessionId(),
            startTime: Date.now(),
            userAgent: navigator.userAgent,
            viewport: { width: window.innerWidth, height: window.innerHeight },
            referrer: document.referrer
        };
        this.init();
    }

    init() {
        this.createFeedbackWidgets();
        this.bindEventListeners();
        this.startSessionTracking();
        this.loadPendingFeedback();
    }

    createFeedbackWidgets() {
        // Create floating feedback button
        this.createFloatingFeedbackButton();
        
        // Create contextual feedback prompts
        this.createContextualFeedbackPrompts();
        
        // Create quick feedback tooltips
        this.createQuickFeedbackTooltips();
    }

    createFloatingFeedbackButton() {
        const button = document.createElement('div');
        button.id = 'ai-feedback-float';
        button.className = 'ai-feedback-float';
        button.innerHTML = `
            <div class="feedback-button-content">
                <span class="feedback-icon">💬</span>
                <span class="feedback-text">Feedback</span>
            </div>
        `;
        
        button.addEventListener('click', () => this.showFeedbackModal());
        document.body.appendChild(button);
    }

    createContextualFeedbackPrompts() {
        // Add "Was this helpful?" prompts to key content areas
        const contentAreas = document.querySelectorAll('.content-section, .feature-card, .beta-platform-card, .legal-section');

        contentAreas.forEach((area, index) => {
            // Skip if already has a feedback prompt
            if (area.querySelector('.contextual-feedback-prompt')) {
                return;
            }

            const prompt = document.createElement('div');
            prompt.className = 'contextual-feedback-prompt';
            prompt.innerHTML = `
                <div class="feedback-prompt-content">
                    <span>Was this helpful?</span>
                    <div class="feedback-quick-actions">
                        <button class="feedback-quick-btn positive" data-feedback="helpful" data-context="content-${index}" title="Yes, this was helpful">
                            👍 Yes
                        </button>
                        <button class="feedback-quick-btn negative" data-feedback="not-helpful" data-context="content-${index}" title="No, this wasn't helpful">
                            👎 No
                        </button>
                    </div>
                </div>
            `;

            area.appendChild(prompt);

            // Add hover event listeners for better UX
            area.addEventListener('mouseenter', () => {
                prompt.style.opacity = '1';
                prompt.style.visibility = 'visible';
                prompt.style.transform = 'translateY(0)';
                prompt.style.pointerEvents = 'auto';
            });

            area.addEventListener('mouseleave', () => {
                // Only hide if no buttons are being hovered
                if (!prompt.matches(':hover')) {
                    prompt.style.opacity = '0';
                    prompt.style.visibility = 'hidden';
                    prompt.style.transform = 'translateY(-10px)';
                    prompt.style.pointerEvents = 'none';
                }
            });

            // Keep prompt visible when hovering over it
            prompt.addEventListener('mouseleave', () => {
                prompt.style.opacity = '0';
                prompt.style.visibility = 'hidden';
                prompt.style.transform = 'translateY(-10px)';
                prompt.style.pointerEvents = 'none';
            });
        });
    }

    createQuickFeedbackTooltips() {
        // Add feedback tooltips to interactive elements
        const interactiveElements = document.querySelectorAll('button, .btn, a[href]');
        
        interactiveElements.forEach(element => {
            element.addEventListener('click', (e) => {
                setTimeout(() => {
                    this.showQuickFeedbackTooltip(e.target);
                }, 2000); // Show after 2 seconds
            });
        });
    }

    showQuickFeedbackTooltip(element) {
        if (Math.random() > 0.3) return; // Show only 30% of the time
        
        const tooltip = document.createElement('div');
        tooltip.className = 'quick-feedback-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <p>How was that experience?</p>
                <div class="quick-feedback-buttons">
                    <button class="quick-feedback-btn" data-rating="5">😊</button>
                    <button class="quick-feedback-btn" data-rating="4">🙂</button>
                    <button class="quick-feedback-btn" data-rating="3">😐</button>
                    <button class="quick-feedback-btn" data-rating="2">🙁</button>
                    <button class="quick-feedback-btn" data-rating="1">😞</button>
                </div>
            </div>
        `;
        
        // Position tooltip near the element
        const rect = element.getBoundingClientRect();
        tooltip.style.position = 'fixed';
        tooltip.style.top = `${rect.bottom + 10}px`;
        tooltip.style.left = `${rect.left}px`;
        
        document.body.appendChild(tooltip);
        
        // Auto-remove after 5 seconds
        setTimeout(() => tooltip.remove(), 5000);
        
        // Handle rating clicks
        tooltip.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-feedback-btn')) {
                const rating = e.target.dataset.rating;
                this.recordQuickFeedback(element, rating);
                tooltip.remove();
            }
        });
    }

    showFeedbackModal() {
        console.log('🎯 Creating feedback modal...');

        // Remove any existing modals first
        const existingModals = document.querySelectorAll('.ai-feedback-modal');
        existingModals.forEach(modal => modal.remove());

        const modal = document.createElement('div');
        modal.className = 'ai-feedback-modal';
        modal.style.zIndex = '10000'; // Ensure it's on top
        modal.innerHTML = `
            <div class="feedback-modal-overlay"></div>
            <div class="feedback-modal-content">
                <div class="feedback-modal-header">
                    <h3>Share Your Feedback</h3>
                    <button class="feedback-modal-close">&times;</button>
                </div>
                <div class="feedback-modal-body">
                    <div class="feedback-category-selection">
                        <h4>What type of feedback do you have?</h4>
                        <div class="feedback-categories">
                            <button class="feedback-category-btn" data-category="suggestion">💡 Suggestion</button>
                            <button class="feedback-category-btn" data-category="bug">🐛 Bug Report</button>
                            <button class="feedback-category-btn" data-category="feature">🚀 Feature Request</button>
                            <button class="feedback-category-btn" data-category="general">💬 General</button>
                        </div>
                    </div>

                    <div class="feedback-form" style="display: none;">
                        <div class="feedback-rating">
                            <h4>Overall Experience</h4>
                            <div class="star-rating">
                                <span class="star" data-rating="1">⭐</span>
                                <span class="star" data-rating="2">⭐</span>
                                <span class="star" data-rating="3">⭐</span>
                                <span class="star" data-rating="4">⭐</span>
                                <span class="star" data-rating="5">⭐</span>
                            </div>
                        </div>

                        <div class="feedback-text">
                            <h4>Tell us more</h4>
                            <textarea id="feedback-message" placeholder="Share your thoughts, suggestions, or report issues..."></textarea>
                        </div>

                        <div class="feedback-context">
                            <h4>Context (Optional)</h4>
                            <label><input type="checkbox" id="include-page-info"> Include current page information</label>
                            <label><input type="checkbox" id="include-browser-info"> Include browser information</label>
                            <label><input type="checkbox" id="include-usage-data"> Include usage patterns (anonymous)</label>
                        </div>

                        <div class="feedback-actions">
                            <button class="feedback-submit-btn">Submit Feedback</button>
                            <button class="feedback-cancel-btn">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        console.log('✅ Modal added to DOM');

        // Force display and check visibility
        setTimeout(() => {
            const categorySelection = modal.querySelector('.feedback-category-selection');
            const categoryButtons = modal.querySelectorAll('.feedback-category-btn');

            console.log('Modal visibility check:', {
                modalVisible: modal.offsetParent !== null,
                categorySelectionVisible: categorySelection ? categorySelection.offsetParent !== null : false,
                categoryButtonsCount: categoryButtons.length,
                categoryButtonsVisible: Array.from(categoryButtons).map(btn => btn.offsetParent !== null)
            });

            // Ensure category selection is visible
            if (categorySelection) {
                categorySelection.style.display = 'block';
                categorySelection.style.visibility = 'visible';
            }
        }, 100);

        this.bindFeedbackModalEvents(modal);
    }

    bindFeedbackModalEvents(modal) {
        console.log('🔗 Binding modal events...');

        // Close modal events
        const closeBtn = modal.querySelector('.feedback-modal-close');
        const cancelBtn = modal.querySelector('.feedback-cancel-btn');
        const overlay = modal.querySelector('.feedback-modal-overlay');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                console.log('❌ Close button clicked');
                modal.remove();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                console.log('❌ Cancel button clicked');
                modal.remove();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', () => {
                console.log('❌ Overlay clicked');
                modal.remove();
            });
        }

        // Category selection
        const categoryButtons = modal.querySelectorAll('.feedback-category-btn');
        console.log(`📋 Found ${categoryButtons.length} category buttons`);

        categoryButtons.forEach((btn, index) => {
            console.log(`📋 Binding category button ${index + 1}: ${btn.textContent}`);
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                console.log(`✅ Category selected: ${category}`);

                const categorySelection = modal.querySelector('.feedback-category-selection');
                const feedbackForm = modal.querySelector('.feedback-form');

                if (categorySelection && feedbackForm) {
                    categorySelection.style.display = 'none';
                    feedbackForm.style.display = 'block';
                    feedbackForm.dataset.category = category;
                    console.log('🔄 Switched to feedback form');
                } else {
                    console.error('❌ Could not find category selection or feedback form elements');
                }
            });
        });

        // Star rating
        const stars = modal.querySelectorAll('.star');
        console.log(`⭐ Found ${stars.length} star rating elements`);

        stars.forEach(star => {
            star.addEventListener('click', (e) => {
                const rating = e.target.dataset.rating;
                console.log(`⭐ Rating selected: ${rating}`);

                modal.querySelectorAll('.star').forEach((s, index) => {
                    s.style.opacity = index < rating ? '1' : '0.3';
                });
                modal.querySelector('.feedback-form').dataset.rating = rating;
            });
        });

        // Submit feedback
        const submitBtn = modal.querySelector('.feedback-submit-btn');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => {
                console.log('📤 Submit feedback clicked');
                this.submitDetailedFeedback(modal);
            });
        }

        console.log('✅ All modal events bound successfully');
    }

    submitDetailedFeedback(modal) {
        const form = modal.querySelector('.feedback-form');
        const category = form.dataset.category;
        const rating = form.dataset.rating;
        const message = modal.querySelector('#feedback-message').value;
        const includePageInfo = modal.querySelector('#include-page-info').checked;
        const includeBrowserInfo = modal.querySelector('#include-browser-info').checked;
        const includeUsageData = modal.querySelector('#include-usage-data').checked;
        
        const feedbackData = {
            type: 'detailed',
            category,
            rating: parseInt(rating) || null,
            message,
            metadata: this.collectMetadata(includePageInfo, includeBrowserInfo, includeUsageData),
            timestamp: Date.now(),
            sessionId: this.sessionData.sessionId
        };
        
        this.recordFeedback(feedbackData);
        this.showFeedbackThankYou(modal);
    }

    recordQuickFeedback(element, rating) {
        const feedbackData = {
            type: 'quick',
            rating: parseInt(rating),
            element: {
                tagName: element.tagName,
                className: element.className,
                id: element.id,
                text: element.textContent.substring(0, 50)
            },
            metadata: this.collectMetadata(true, false, false),
            timestamp: Date.now(),
            sessionId: this.sessionData.sessionId
        };
        
        this.recordFeedback(feedbackData);
    }

    recordContextualFeedback(context, feedback) {
        const feedbackData = {
            type: 'contextual',
            context,
            feedback,
            metadata: this.collectMetadata(true, false, false),
            timestamp: Date.now(),
            sessionId: this.sessionData.sessionId
        };
        
        this.recordFeedback(feedbackData);
    }

    recordFeedback(feedbackData) {
        // Add to queue
        this.feedbackQueue.push(feedbackData);
        
        // Store locally
        this.storeFeedbackLocally(feedbackData);
        
        // Attempt to send to backend
        this.sendFeedbackToBackend(feedbackData);
        
        // Trigger analytics
        this.triggerFeedbackAnalytics(feedbackData);
    }

    collectMetadata(includePageInfo, includeBrowserInfo, includeUsageData) {
        const metadata = {
            timestamp: Date.now(),
            sessionId: this.sessionData.sessionId
        };
        
        if (includePageInfo) {
            metadata.page = {
                url: window.location.href,
                title: document.title,
                referrer: document.referrer,
                scrollPosition: window.pageYOffset,
                viewport: { width: window.innerWidth, height: window.innerHeight }
            };
        }
        
        if (includeBrowserInfo) {
            metadata.browser = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled
            };
        }
        
        if (includeUsageData) {
            metadata.usage = {
                timeOnPage: Date.now() - this.sessionData.startTime,
                clickCount: this.getClickCount(),
                scrollDepth: this.getScrollDepth(),
                visitHistory: this.getVisitHistory()
            };
        }
        
        return metadata;
    }

    storeFeedbackLocally(feedbackData) {
        const existingFeedback = JSON.parse(localStorage.getItem('promz_feedback_data') || '[]');
        existingFeedback.push(feedbackData);
        
        // Keep only last 100 feedback items
        if (existingFeedback.length > 100) {
            existingFeedback.shift();
        }
        
        localStorage.setItem('promz_feedback_data', JSON.stringify(existingFeedback));
    }

    sendFeedbackToBackend(feedbackData) {
        fetch('/api/feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(feedbackData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Feedback sent successfully:', data);
            this.markFeedbackAsSent(feedbackData);
        })
        .catch(error => {
            console.log('Backend not available, feedback stored locally');
            this.markFeedbackAsPending(feedbackData);
        });
    }

    triggerFeedbackAnalytics(feedbackData) {
        // Trigger analytics events for feedback patterns
        if (window.gtag) {
            gtag('event', 'feedback_submitted', {
                feedback_type: feedbackData.type,
                feedback_category: feedbackData.category,
                feedback_rating: feedbackData.rating
            });
        }
        
        // Custom analytics
        this.analyzeFeedbackPatterns(feedbackData);
    }

    analyzeFeedbackPatterns(feedbackData) {
        const patterns = JSON.parse(localStorage.getItem('promz_feedback_patterns') || '{}');
        
        // Track feedback frequency
        patterns.totalFeedback = (patterns.totalFeedback || 0) + 1;
        patterns.byType = patterns.byType || {};
        patterns.byType[feedbackData.type] = (patterns.byType[feedbackData.type] || 0) + 1;
        
        // Track ratings
        if (feedbackData.rating) {
            patterns.ratings = patterns.ratings || [];
            patterns.ratings.push(feedbackData.rating);
            
            // Calculate average rating
            patterns.averageRating = patterns.ratings.reduce((a, b) => a + b, 0) / patterns.ratings.length;
        }
        
        localStorage.setItem('promz_feedback_patterns', JSON.stringify(patterns));
    }

    showFeedbackThankYou(modal) {
        modal.querySelector('.feedback-modal-body').innerHTML = `
            <div class="feedback-thank-you">
                <h3>🎉 Thank you!</h3>
                <p>Your feedback has been received and will help us improve Promz.</p>
                <p>We appreciate you taking the time to share your thoughts!</p>
                <button class="feedback-close-btn">Close</button>
            </div>
        `;
        
        modal.querySelector('.feedback-close-btn').addEventListener('click', () => modal.remove());
        
        // Auto-close after 3 seconds
        setTimeout(() => modal.remove(), 3000);
    }

    bindEventListeners() {
        // Bind contextual feedback events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('feedback-quick-btn')) {
                const feedback = e.target.dataset.feedback;
                const context = e.target.dataset.context;
                this.recordContextualFeedback(context, feedback);

                // Visual feedback
                e.target.style.background = feedback === 'helpful' ? '#4CAF50' : '#f44336';
                e.target.style.color = 'white';
                e.target.style.transform = 'scale(1.1)';

                // Disable both buttons
                const prompt = e.target.closest('.contextual-feedback-prompt');
                const allButtons = prompt.querySelectorAll('.feedback-quick-btn');
                allButtons.forEach(btn => {
                    btn.disabled = true;
                    btn.style.cursor = 'default';
                });

                // Show thank you message
                const promptContent = prompt.querySelector('.feedback-prompt-content');
                setTimeout(() => {
                    promptContent.innerHTML = `
                        <span style="color: #4CAF50; font-weight: 500;">
                            ✓ Thank you for your feedback!
                        </span>
                    `;
                }, 500);

                // Fade out the prompt after showing thank you
                setTimeout(() => {
                    prompt.style.opacity = '0';
                    prompt.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        prompt.style.display = 'none';
                    }, 300);
                }, 2500);
            }
        });
    }

    // Utility methods
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getClickCount() {
        const clickHistory = JSON.parse(localStorage.getItem('promz_click_history') || '[]');
        return clickHistory.length;
    }

    getScrollDepth() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        return Math.round((scrollTop / docHeight) * 100);
    }

    getVisitHistory() {
        return JSON.parse(localStorage.getItem('promz_visit_history') || '[]');
    }

    markFeedbackAsSent(feedbackData) {
        feedbackData.status = 'sent';
        feedbackData.sentAt = Date.now();
    }

    markFeedbackAsPending(feedbackData) {
        feedbackData.status = 'pending';
    }

    loadPendingFeedback() {
        // Attempt to send any pending feedback
        const pendingFeedback = JSON.parse(localStorage.getItem('promz_feedback_data') || '[]')
            .filter(feedback => feedback.status === 'pending');
        
        pendingFeedback.forEach(feedback => {
            this.sendFeedbackToBackend(feedback);
        });
    }

    startSessionTracking() {
        // Track session duration
        window.addEventListener('beforeunload', () => {
            const sessionDuration = Date.now() - this.sessionData.startTime;
            localStorage.setItem('promz_last_session_duration', sessionDuration.toString());
        });
    }
}

// Initialize AI Feedback System when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.aiFeedbackSystem = new AIFeedbackSystem();
});
