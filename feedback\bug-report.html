<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Report a Bug</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
    <style>
        .feedback-form {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-lg, 2rem);
            background-color: white;
            border-radius: var(--border-radius, 0.375rem);
            box-shadow: var(--box-shadow, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        input[type="text"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color, #e5eaef);
            border-radius: var(--border-radius, 0.375rem);
            font-size: 16px;
            font-family: var(--font-primary, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif);
        }

        textarea {
            min-height: 150px;
            resize: vertical;
        }

        button {
            background-color: var(--primary-color, #4a6cf7);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius, 0.375rem);
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: var(--primary-dark, #3959d9);
        }

        .success-message {
            display: none;
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .file-upload {
            margin-top: 5px;
        }

        .file-upload-label {
            display: inline-block;
            padding: 8px 16px;
            background-color: #e0e0e0;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
        }

        .file-upload-label:hover {
            background-color: #d0d0d0;
        }

        .file-name {
            margin-left: 10px;
            font-size: 14px;
        }

        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('../includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the header for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="../beta/index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="index.html"');

                document.getElementById('header-placeholder').innerHTML = data;

                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                    if (link.href.includes('feedback/') || link.textContent.trim() === 'Feedback') {
                        link.classList.add('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="../index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="../index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="../about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="../download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="../beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="index.html" style="color: #4a6cf7; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="hero">
        <div class="container">
            <h1>Report a Bug</h1>
            <p>Found a bug in Promz? Help us improve by reporting it here.</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="bugReportForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="bugTitle">Bug Title</label>
                        <input type="text" id="bugTitle" name="bugTitle" required placeholder="A brief description of the issue">
                    </div>

                    <div class="form-group">
                        <label for="severity">Severity</label>
                        <select id="severity" name="severity">
                            <option value="Low">Low - Minor inconvenience</option>
                            <option value="Medium" selected>Medium - Affects functionality but has workaround</option>
                            <option value="High">High - Major functionality broken</option>
                            <option value="Critical">Critical - Application crashes or data loss</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="appVersion">App Version</label>
                        <input type="text" id="appVersion" name="appVersion" placeholder="e.g., 1.2.3">
                    </div>

                    <div class="form-group">
                        <label for="platform">Platform</label>
                        <select id="platform" name="platform">
                            <option value="Windows">Windows</option>
                            <option value="macOS">macOS</option>
                            <option value="Linux">Linux</option>
                            <option value="iOS">iOS</option>
                            <option value="Android">Android</option>
                            <option value="Web">Web</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Bug Description</label>
                        <textarea id="description" name="description" required placeholder="Please describe the bug in detail. What happened? What did you expect to happen? How can we reproduce it?"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="stepsToReproduce">Steps to Reproduce</label>
                        <textarea id="stepsToReproduce" name="stepsToReproduce" placeholder="1. Open the app&#10;2. Navigate to...&#10;3. Click on..."></textarea>
                    </div>

                    <div class="form-group">
                        <label>Screenshot or Video (optional)</label>
                        <div class="file-upload">
                            <label for="fileInput" class="file-upload-label">
                                <i class="fas fa-upload"></i> Choose File
                            </label>
                            <span id="fileName" class="file-name">No file chosen</span>
                            <input type="file" id="fileInput" name="fileInput" accept="image/*,video/*">
                        </div>
                        <small>Please attach any screenshots or videos that help illustrate the issue.</small>
                    </div>

                    <button type="submit">Submit Bug Report</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for reporting this bug! Our team will investigate and work on a fix.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error submitting your bug report. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('../includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the footer for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="../beta/index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="index.html"');
                data = data.replace(/href="terms\.html"/g, 'href="../terms.html"');
                data = data.replace(/href="privacy\.html"/g, 'href="../privacy.html"');

                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="../about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="../download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="../beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="../privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script>
        // Update file name display when a file is selected
        document.getElementById('fileInput').addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'No file chosen';
            document.getElementById('fileName').textContent = fileName;
        });

        document.getElementById('bugReportForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const bugTitle = document.getElementById('bugTitle').value;
            const severity = document.getElementById('severity').value;
            const appVersion = document.getElementById('appVersion').value;
            const platform = document.getElementById('platform').value;
            const description = document.getElementById('description').value;
            const stepsToReproduce = document.getElementById('stepsToReproduce').value;

            // Construct email subject and body
            const subject = `Bug Report: ${bugTitle}`;
            let body = `Name: ${name}\nEmail: ${email}\nSeverity: ${severity}\nApp Version: ${appVersion}\nPlatform: ${platform}\n\n`;
            body += `Description:\n${description}\n\n`;
            body += `Steps to Reproduce:\n${stepsToReproduce}\n\n`;
            body += `Note: If you have screenshots, please attach them to this email.`;

            // Create mailto link
            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

            // Open email client
            window.location.href = mailtoLink;

            // Show success message
            document.getElementById('successMessage').style.display = 'block';

            // Reset form
            document.getElementById('bugReportForm').reset();
            document.getElementById('fileName').textContent = 'No file chosen';
        });
    </script>
</body>
</html>
