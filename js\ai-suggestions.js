/**
 * AI-Powered Content Suggestions Engine
 * Analyzes page context and provides intelligent recommendations
 */

class AISuggestionsEngine {
    constructor() {
        this.suggestions = [];
        this.userContext = {
            currentPage: window.location.pathname,
            timeOnPage: 0,
            interactions: [],
            preferences: this.loadUserPreferences(),
            visitHistory: this.loadVisitHistory()
        };
        this.suggestionRules = this.initializeSuggestionRules();
        this.init();
    }

    init() {
        this.trackPageContext();
        this.startTimeTracking();
        this.bindEventListeners();
        this.generateInitialSuggestions();
        this.createSuggestionsUI();
    }

    // Track page context and user behavior
    trackPageContext() {
        // Analyze current page content
        const pageTitle = document.title;
        const pageContent = document.body.innerText.toLowerCase();
        const forms = document.querySelectorAll('form');
        const buttons = document.querySelectorAll('button, .btn');
        
        this.userContext.pageAnalysis = {
            title: pageTitle,
            hasForm: forms.length > 0,
            buttonCount: buttons.length,
            contentKeywords: this.extractKeywords(pageContent),
            pageType: this.determinePageType()
        };

        // Update visit history
        this.updateVisitHistory();
    }

    extractKeywords(content) {
        const keywords = ['prompt', 'ai', 'feedback', 'beta', 'download', 'help', 'support'];
        return keywords.filter(keyword => content.includes(keyword));
    }

    determinePageType() {
        const path = window.location.pathname;
        if (path.includes('feedback')) return 'feedback';
        if (path.includes('beta')) return 'beta';
        if (path.includes('download')) return 'download';
        if (path.includes('about')) return 'about';
        if (path === '/' || path.includes('index')) return 'home';
        return 'other';
    }

    // Initialize suggestion rules based on context
    initializeSuggestionRules() {
        return {
            home: [
                {
                    condition: () => this.userContext.timeOnPage > 30000,
                    suggestion: {
                        type: 'tip',
                        title: 'New to Promz?',
                        content: 'Check out our getting started guide to make the most of AI prompts.',
                        action: { text: 'Learn More', url: 'about.html' }
                    }
                },
                {
                    condition: () => this.userContext.interactions.length === 0,
                    suggestion: {
                        type: 'prompt',
                        title: 'Try Our Beta!',
                        content: 'Join our beta program for early access to new features.',
                        action: { text: 'Join Beta', url: 'beta/index.html' }
                    }
                }
            ],
            feedback: [
                {
                    condition: () => this.userContext.timeOnPage > 60000,
                    suggestion: {
                        type: 'help',
                        title: 'Need Help?',
                        content: 'Having trouble with the form? Try our quick help guide.',
                        action: { text: 'Get Help', url: '#help-modal' }
                    }
                }
            ],
            download: [
                {
                    condition: () => this.userContext.timeOnPage > 45000,
                    suggestion: {
                        type: 'alternative',
                        title: 'Having Download Issues?',
                        content: 'Try our web version or contact support for assistance.',
                        action: { text: 'Contact Support', url: 'feedback/index.html' }
                    }
                }
            ],
            beta: [
                {
                    condition: () => true,
                    suggestion: {
                        type: 'info',
                        title: 'Beta Feedback',
                        content: 'Your feedback helps us improve! Share your experience.',
                        action: { text: 'Give Feedback', url: 'feedback/index.html' }
                    }
                }
            ]
        };
    }

    // Generate suggestions based on current context
    generateSuggestions() {
        const pageType = this.userContext.pageAnalysis.pageType;
        const rules = this.suggestionRules[pageType] || [];
        
        this.suggestions = rules
            .filter(rule => rule.condition())
            .map(rule => ({
                ...rule.suggestion,
                id: this.generateSuggestionId(),
                timestamp: Date.now(),
                context: pageType
            }));

        // Add dynamic suggestions based on user behavior
        this.addBehaviorBasedSuggestions();
        
        return this.suggestions;
    }

    addBehaviorBasedSuggestions() {
        // Suggest related pages based on visit history
        if (this.userContext.visitHistory.length > 1) {
            const lastPage = this.userContext.visitHistory[this.userContext.visitHistory.length - 2];
            if (lastPage.includes('feedback') && !window.location.pathname.includes('beta')) {
                this.suggestions.push({
                    id: this.generateSuggestionId(),
                    type: 'related',
                    title: 'Interested in Beta Testing?',
                    content: 'Since you visited feedback, you might want to join our beta program.',
                    action: { text: 'Join Beta', url: 'beta/index.html' },
                    timestamp: Date.now(),
                    context: 'behavioral'
                });
            }
        }

        // Suggest help if user seems stuck
        if (this.userContext.timeOnPage > 120000 && this.userContext.interactions.length < 2) {
            this.suggestions.push({
                id: this.generateSuggestionId(),
                type: 'help',
                title: 'Looking for Something?',
                content: 'You\'ve been here a while. Can we help you find what you need?',
                action: { text: 'Get Help', url: 'feedback/index.html' },
                timestamp: Date.now(),
                context: 'behavioral'
            });
        }
    }

    generateInitialSuggestions() {
        setTimeout(() => {
            this.generateSuggestions();
            this.displaySuggestions();
        }, 5000); // Wait 5 seconds before showing initial suggestions
    }

    // Create suggestions UI
    createSuggestionsUI() {
        if (document.getElementById('ai-suggestions-container')) return;

        const container = document.createElement('div');
        container.id = 'ai-suggestions-container';
        container.innerHTML = `
            <div id="ai-suggestions-panel" class="ai-suggestions-panel">
                <div class="ai-suggestions-header">
                    <h4>💡 AI Suggestions</h4>
                    <button id="ai-suggestions-close" class="ai-suggestions-close">&times;</button>
                </div>
                <div id="ai-suggestions-content" class="ai-suggestions-content">
                    <!-- Suggestions will be populated here -->
                </div>
            </div>
        `;

        document.body.appendChild(container);
        this.bindSuggestionsEvents();
    }

    displaySuggestions() {
        const content = document.getElementById('ai-suggestions-content');
        const panel = document.getElementById('ai-suggestions-panel');
        
        if (!content || this.suggestions.length === 0) return;

        content.innerHTML = this.suggestions.map(suggestion => `
            <div class="ai-suggestion-item" data-suggestion-id="${suggestion.id}">
                <div class="ai-suggestion-icon">${this.getSuggestionIcon(suggestion.type)}</div>
                <div class="ai-suggestion-content">
                    <h5>${suggestion.title}</h5>
                    <p>${suggestion.content}</p>
                    ${suggestion.action ? `
                        <button class="ai-suggestion-action" data-url="${suggestion.action.url}">
                            ${suggestion.action.text}
                        </button>
                    ` : ''}
                </div>
                <div class="ai-suggestion-feedback">
                    <button class="ai-feedback-btn" data-feedback="helpful" data-suggestion-id="${suggestion.id}">👍</button>
                    <button class="ai-feedback-btn" data-feedback="not-helpful" data-suggestion-id="${suggestion.id}">👎</button>
                </div>
            </div>
        `).join('');

        panel.classList.add('show');
    }

    getSuggestionIcon(type) {
        const icons = {
            tip: '💡',
            help: '❓',
            prompt: '🚀',
            alternative: '🔄',
            info: 'ℹ️',
            related: '🔗'
        };
        return icons[type] || '💡';
    }

    bindSuggestionsEvents() {
        // Close button
        document.getElementById('ai-suggestions-close').addEventListener('click', () => {
            document.getElementById('ai-suggestions-panel').classList.remove('show');
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('ai-suggestion-action')) {
                const url = e.target.dataset.url;
                if (url.startsWith('#')) {
                    // Handle modal or anchor
                    this.handleModalAction(url);
                } else {
                    window.location.href = url;
                }
            }

            if (e.target.classList.contains('ai-feedback-btn')) {
                this.handleFeedback(e.target);
            }
        });
    }

    handleFeedback(button) {
        const suggestionId = button.dataset.suggestionId;
        const feedback = button.dataset.feedback;
        
        // Record feedback
        this.recordFeedback(suggestionId, feedback);
        
        // Visual feedback
        button.style.background = feedback === 'helpful' ? '#4CAF50' : '#f44336';
        button.style.color = 'white';
        
        // Disable both buttons for this suggestion
        const suggestionItem = button.closest('.ai-suggestion-item');
        suggestionItem.querySelectorAll('.ai-feedback-btn').forEach(btn => {
            btn.disabled = true;
        });

        setTimeout(() => {
            suggestionItem.style.opacity = '0.7';
        }, 1000);
    }

    // Utility methods
    generateSuggestionId() {
        return 'suggestion_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    startTimeTracking() {
        this.startTime = Date.now();
        setInterval(() => {
            this.userContext.timeOnPage = Date.now() - this.startTime;
        }, 1000);
    }

    bindEventListeners() {
        // Track user interactions
        document.addEventListener('click', (e) => {
            this.userContext.interactions.push({
                type: 'click',
                element: e.target.tagName,
                timestamp: Date.now()
            });
        });

        // Track form interactions
        document.addEventListener('input', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                this.userContext.interactions.push({
                    type: 'input',
                    element: e.target.name || e.target.id,
                    timestamp: Date.now()
                });
                
                // Regenerate suggestions on input change
                clearTimeout(this.inputTimeout);
                this.inputTimeout = setTimeout(() => {
                    this.generateSuggestions();
                    this.displaySuggestions();
                }, 2000);
            }
        });
    }

    loadUserPreferences() {
        return JSON.parse(localStorage.getItem('promz_user_preferences') || '{}');
    }

    loadVisitHistory() {
        return JSON.parse(localStorage.getItem('promz_visit_history') || '[]');
    }

    updateVisitHistory() {
        const history = this.loadVisitHistory();
        history.push({
            path: window.location.pathname,
            timestamp: Date.now(),
            title: document.title
        });
        
        // Keep only last 10 visits
        if (history.length > 10) {
            history.shift();
        }
        
        localStorage.setItem('promz_visit_history', JSON.stringify(history));
        this.userContext.visitHistory = history;
    }

    recordFeedback(suggestionId, feedback) {
        const feedbackData = {
            suggestionId,
            feedback,
            timestamp: Date.now(),
            page: window.location.pathname,
            userContext: this.userContext
        };

        // Store locally
        const existingFeedback = JSON.parse(localStorage.getItem('promz_ai_feedback') || '[]');
        existingFeedback.push(feedbackData);
        localStorage.setItem('promz_ai_feedback', JSON.stringify(existingFeedback));

        // Send to backend (if available)
        this.sendFeedbackToBackend(feedbackData);
    }

    sendFeedbackToBackend(feedbackData) {
        // This would send to your backend API
        fetch('/api/ai-feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(feedbackData)
        }).catch(error => {
            console.log('Backend not available, feedback stored locally');
        });
    }

    handleModalAction(url) {
        // Handle modal actions like help
        if (url === '#help-modal') {
            this.showHelpModal();
        }
    }

    showHelpModal() {
        // Create and show help modal
        const modal = document.createElement('div');
        modal.className = 'ai-help-modal';
        modal.innerHTML = `
            <div class="ai-help-modal-content">
                <h3>Need Help?</h3>
                <p>Here are some quick tips:</p>
                <ul>
                    <li>Use the navigation menu to explore different sections</li>
                    <li>Check out our beta program for early access</li>
                    <li>Submit feedback to help us improve</li>
                </ul>
                <button onclick="this.closest('.ai-help-modal').remove()">Close</button>
            </div>
        `;
        document.body.appendChild(modal);
    }
}

// Initialize AI Suggestions Engine when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.aiSuggestions = new AISuggestionsEngine();
});
