# BACKUP: Original Inconsistent Code Reference

This document shows what the code looked like BEFORE the consistency fixes were applied.

## CRITICAL INCONSISTENCIES THAT WERE FIXED

### 1. HARDCODED HEADERS (Instead of Dynamic Loading)

#### 404.html - BEFORE FIX:
```html
<header>
    <div class="container">
        <div class="logo">
            <a href="index.html">
                <img src="images/logo.png" alt="Promz Logo">
            </a>
        </div>
        <nav>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="privacy.html">Privacy Policy</a></li>
                <li><a href="terms.html">Terms of Service</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </nav>
    </div>
</header>
```

#### feedback/bug-report.html - BEFORE FIX:
```html
<header>
    <div class="container">
        <div class="logo">
            <a href="../index.html">
                <img src="../images/logo.png" alt="Promz Logo">
            </a>
        </div>
        <nav>
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="../about.html">About</a></li>
                <li><a href="../download.html">Download</a></li>
                <li><a href="../beta/index.html">Beta Program</a></li>
                <li><a href="index.html" class="active">Feedback</a></li>
            </ul>
        </nav>
    </div>
</header>
```

### 2. HARDCODED FOOTERS (Instead of Dynamic Loading)

#### 404.html - BEFORE FIX:
```html
<footer>
    <div class="container">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="images/logo.png" alt="Promz Logo">
                <p>&copy; <span class="copyright-year">2025</span> Promz. All rights reserved.</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="privacy.html">Privacy Policy</a></li>
                    <li><a href="terms.html">Terms of Service</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h3>Contact Us</h3>
                <p>Email: <EMAIL></p>
                <div class="social-links">
                    <a href="#" class="social-icon">Twitter</a>
                    <a href="#" class="social-icon">GitHub</a>
                    <a href="#" class="social-icon">LinkedIn</a>
                </div>
            </div>
        </div>
    </div>
</footer>
```

### 3. BRAND NAME INCONSISTENCIES

#### download.html - BEFORE FIX:
```html
<title>Download PromZ - AI Made Easy</title>
<h1>Someone shared a prompt with you!</h1>
<p>Download PromZ to access this prompt and many more.</p>
<h2>Download PromZ</h2>
<p>PromZ is an AI assistant that helps you create, manage, and share prompts...</p>
```

#### redirect.html - BEFORE FIX:
```html
<title>PromZ - Redirecting...</title>
<h1>Redirecting to PromZ</h1>
```

### 4. FAVICON INCONSISTENCIES

#### download.html - BEFORE FIX:
```html
<link rel="icon" href="favicon.ico">
```

#### redirect.html - BEFORE FIX:
```html
<link rel="icon" href="/favicon.ico">
```

### 5. MISSING APPLE TOUCH ICONS

#### Multiple files were missing:
```html
<link rel="apple-touch-icon" href="images/apple-touch-icon.png">
```

Files affected: 404.html, contact.html, privacy.html, terms.html, beta/index.html, all feedback pages, delete-me/index.html, redirect.html

### 6. BROKEN RELATIVE PATHS

#### beta/index.html - BEFORE FIX:
```html
<a href="../../feedback/index.html" class="btn secondary">Request Early Access</a>
<li><a href="../../feedback/index.html">General Feedback</a></li>
<li><a href="../../feedback/feature-request.html">Feature Requests</a></li>
<li><a href="../../feedback/bug-report.html">Bug Reports</a></li>
<a href="../../feedback/index.html">contact us</a>
```

### 7. HTML SYNTAX ERRORS

#### releases/index.html - BEFORE FIX:
```html
<li>Reset or update session data when the token expires and the user signs out/li>
```
(Missing closing `>`)

### 8. BROKEN DOWNLOAD LINKS

#### index.html - BEFORE FIX:
```html
<a href="#download" class="btn primary">Download Now</a>
<a href="#" class="download-button">Android</a>
<a href="#" class="download-button">iOS</a>
<a href="#" class="download-button">Desktop</a>
```

#### download.html - BEFORE FIX:
```html
<a href="#" class="download-button" id="ios-download">
<a href="#" class="download-button" id="android-download">
<a href="#" class="download-button" id="windows-download">
```
(JavaScript only set hrefs when shortId existed)

### 9. MISSING JAVASCRIPT FILES

#### releases/index.html - BEFORE FIX:
Missing: `<script src="../js/main.js"></script>`

#### redirect.html - BEFORE FIX:
Missing: `<script src="js/main.js"></script>`

### 10. BROKEN FEEDBACK LINKS

#### releases/index.html - BEFORE FIX:
```html
<a href="/feedback/index.html">feedback form</a>
```
(Should be relative: `../feedback/index.html`)

## FILES THAT HAD HARDCODED HEADERS/FOOTERS:
- 404.html
- feedback/bug-report.html  
- feedback/feature-request.html
- delete-me/index.html

## FILES WITH BRAND NAME ISSUES:
- download.html (used "PromZ")
- redirect.html (used "PromZ")

## FILES WITH FAVICON ISSUES:
- download.html (used favicon.ico)
- redirect.html (used /favicon.ico)

This reference shows the state before all consistency fixes were applied.
