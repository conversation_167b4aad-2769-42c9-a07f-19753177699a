<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - Promz</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/ai-components.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('terms.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>Terms of Service</h1>
            <p>Last updated: April 14, 2025</p>
        </div>
    </section>

    <section class="legal-content">
        <div class="container">
            <div class="legal-section">
                <h2>1. Acceptance of Terms</h2>
                <p>By accessing or using Promz, including any related applications, websites, or services (collectively,
                    the "Services"), you agree to be bound by these Terms of Service. If you do not agree to these
                    terms, please do not use our Services.</p>
                <p>These Terms constitute a legally binding agreement between you and Promz regarding your use of the
                    Services.</p>
            </div>

            <div class="legal-section">
                <h2>2. Changes to Terms</h2>
                <p>We reserve the right to modify these Terms at any time. We will provide notice of any material
                    changes by updating the "Last Updated" date at the top of these Terms and/or by sending a notice to
                    the email address associated with your account.</p>
                <p>Your continued use of the Services after such modifications constitutes your acceptance of the
                    revised Terms.</p>
            </div>

            <div class="legal-section">
                <h2>3. Account Registration</h2>
                <p>To access certain features of the Services, you may be required to register for an account. You agree
                    to provide accurate, current, and complete information during the registration process and to update
                    such information to keep it accurate, current, and complete.</p>
                <p>You are responsible for safeguarding your password and for all activities that occur under your
                    account. You agree to notify us immediately of any unauthorized use of your account.</p>
            </div>

            <div class="legal-section">
                <h2>4. User Content</h2>
                <p>The Services allow you to create, upload, store, and share content, including prompt templates,
                    configurations, and other materials (collectively, "User Content").</p>
                <p>You retain ownership of your User Content, but you grant us a worldwide, non-exclusive, royalty-free
                    license to use, reproduce, modify, adapt, publish, and display your User Content in connection with
                    the Services.</p>
                <p>You are solely responsible for your User Content and the consequences of posting or publishing it. By
                    submitting User Content, you represent and warrant that:</p>
                <ul>
                    <li>You own or have the necessary rights to use and authorize others to use your User Content;</li>
                    <li>Your User Content does not violate the rights of any third party, including intellectual
                        property rights, privacy rights, or publicity rights;</li>
                    <li>Your User Content complies with these Terms and all applicable laws and regulations.</li>
                </ul>
            </div>

            <div class="legal-section">
                <h2>5. Prohibited Conduct</h2>
                <p>You agree not to engage in any of the following prohibited activities:</p>
                <ul>
                    <li>Using the Services for any illegal purpose or in violation of any laws;</li>
                    <li>Attempting to interfere with, compromise the system integrity or security, or decipher any
                        transmissions to or from the servers running the Services;</li>
                    <li>Using the Services to send unsolicited communications, promotions, or advertisements, or to
                        spam;</li>
                    <li>Uploading or transmitting viruses, malware, or other types of malicious code;</li>
                    <li>Collecting or harvesting any information from the Services, including user accounts;</li>
                    <li>Impersonating another person or misrepresenting your affiliation with any person or entity;</li>
                    <li>Using the Services in a manner that could disable, overburden, damage, or impair the Services.
                    </li>
                </ul>
            </div>

            <div class="legal-section">
                <h2>6. Intellectual Property Rights</h2>
                <p>The Services and their original content, features, and functionality are owned by Promz and are
                    protected by international copyright, trademark, patent, trade secret, and other intellectual
                    property laws.</p>
                <p>Our trademarks and trade dress may not be used in connection with any product or service without the
                    prior written consent of Promz.</p>
            </div>

            <div class="legal-section">
                <h2>7. Third-Party Services</h2>
                <p>The Services may contain links to third-party websites or services that are not owned or controlled
                    by Promz. We have no control over, and assume no responsibility for, the content, privacy policies,
                    or practices of any third-party websites or services.</p>
                <p>You acknowledge and agree that Promz shall not be responsible or liable, directly or indirectly, for
                    any damage or loss caused or alleged to be caused by or in connection with the use of or reliance on
                    any such content, goods, or services available on or through any such websites or services.</p>
            </div>

            <div class="legal-section">
                <h2>8. Termination</h2>
                <p>We may terminate or suspend your account and access to the Services immediately, without prior notice
                    or liability, for any reason whatsoever, including without limitation if you breach these Terms.</p>
                <p>Upon termination, your right to use the Services will immediately cease. If you wish to terminate
                    your account, you may simply discontinue using the Services or contact us to request account
                    deletion.</p>
            </div>

            <div class="legal-section">
                <h2>9. Limitation of Liability</h2>
                <p>In no event shall Promz, its directors, employees, partners, agents, suppliers, or affiliates, be
                    liable for any indirect, incidental, special, consequential, or punitive damages, including without
                    limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:</p>
                <ul>
                    <li>Your access to or use of or inability to access or use the Services;</li>
                    <li>Any conduct or content of any third party on the Services;</li>
                    <li>Any content obtained from the Services; and</li>
                    <li>Unauthorized access, use, or alteration of your transmissions or content.</li>
                </ul>
            </div>

            <div class="legal-section">
                <h2>10. Disclaimer</h2>
                <p>The Services are provided on an "AS IS" and "AS AVAILABLE" basis. Promz expressly disclaims all
                    warranties of any kind, whether express or implied, including, but not limited to, the implied
                    warranties of merchantability, fitness for a particular purpose, and non-infringement.</p>
                <p>Promz makes no warranty that the Services will meet your requirements, be available on an
                    uninterrupted, secure, or error-free basis, or that defects will be corrected.</p>
            </div>

            <div class="legal-section">
                <h2>11. Governing Law</h2>
                <p>These Terms shall be governed and construed in accordance with the laws of [Your Jurisdiction],
                    without regard to its conflict of law provisions.</p>
                <p>Our failure to enforce any right or provision of these Terms will not be considered a waiver of those
                    rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the
                    remaining provisions of these Terms will remain in effect.</p>
            </div>

            <div class="legal-section content-section">
                <h2>12. Contact Us</h2>
                <p>If you have any questions about these Terms, please contact us at:</p>
                <p>Email: <EMAIL></p>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script src="js/main.js"></script>
    <script src="js/ai-suggestions.js"></script>
    <script src="js/workflow-automation.js"></script>
    <script src="js/ai-feedback-system.js"></script>
</body>

</html>