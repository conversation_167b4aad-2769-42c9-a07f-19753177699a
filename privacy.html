<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - Promz</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/ai-components.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('privacy.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>Privacy Policy</h1>
            <p>Last updated: April 14, 2025</p>
        </div>
    </section>

    <section class="legal-content">
        <div class="container">
            <div class="legal-section">
                <h2>1. Introduction</h2>
                <p>Welcome to Promz ("we," "our," or "us"). We are committed to protecting your privacy and ensuring the
                    security of your personal information. This Privacy Policy explains how we collect, use, disclose,
                    and safeguard your information when you use our application, website, and services.</p>
                <p>Please read this Privacy Policy carefully. By accessing or using Promz, you acknowledge that you have
                    read, understood, and agree to be bound by this Privacy Policy. If you do not agree with our
                    policies and practices, please do not use our services.</p>
            </div>

            <div class="legal-section">
                <h2>2. Information We Collect</h2>

                <h3>2.1 Personal Information</h3>
                <p>We may collect personal information that you voluntarily provide to us when you:</p>
                <ul>
                    <li>Create an account or register for our services</li>
                    <li>Submit contact information</li>
                    <li>Request customer support</li>
                    <li>Participate in surveys or promotions</li>
                </ul>
                <p>This information may include:</p>
                <ul>
                    <li>Name</li>
                    <li>Email address</li>
                    <li>Username and password</li>
                    <li>Profile information</li>
                </ul>

                <h3>2.2 Usage Data</h3>
                <p>We automatically collect certain information when you use our application or website, including:</p>
                <ul>
                    <li>Device information (device type, operating system)</li>
                    <li>IP address</li>
                    <li>Browser type and version</li>
                    <li>Pages visited and features used</li>
                    <li>Time and date of access</li>
                    <li>Crash reports and performance data</li>
                </ul>

                <h3>2.3 User Content</h3>
                <p>We collect and store prompt templates, configurations, and other content that you create, upload, or
                    store while using Promz.</p>
            </div>

            <div class="legal-section">
                <h2>3. How We Use Your Information</h2>
                <p>We use the information we collect for various purposes, including to:</p>
                <ul>
                    <li>Provide, maintain, and improve our services</li>
                    <li>Process transactions and manage your account</li>
                    <li>Respond to your inquiries and provide customer support</li>
                    <li>Send you technical notices, updates, and administrative messages</li>
                    <li>Communicate with you about products, services, and events</li>
                    <li>Monitor usage patterns and analyze trends</li>
                    <li>Detect, prevent, and address technical issues</li>
                    <li>Comply with legal obligations</li>
                </ul>
            </div>

            <div class="legal-section">
                <h2>4. Data Sharing and Disclosure</h2>
                <p>We may share your information in the following circumstances:</p>

                <h3>4.1 Service Providers</h3>
                <p>We may share your information with third-party vendors, service providers, and contractors who
                    perform services on our behalf and require access to your information to provide these services.</p>

                <h3>4.2 Business Transfers</h3>
                <p>If we are involved in a merger, acquisition, or asset sale, your information may be transferred as
                    part of that transaction.</p>

                <h3>4.3 Legal Requirements</h3>
                <p>We may disclose your information if required to do so by law or in response to valid requests by
                    public authorities.</p>

                <h3>4.4 Protection of Rights</h3>
                <p>We may disclose your information to protect our rights, privacy, safety, or property, as well as that
                    of our users or others.</p>
            </div>

            <div class="legal-section">
                <h2>5. Data Security</h2>
                <p>We implement appropriate technical and organizational measures to protect your information against
                    unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over
                    the internet or method of electronic storage is 100% secure.</p>
            </div>

            <div class="legal-section">
                <h2>6. Your Rights and Choices</h2>
                <p>Depending on your location, you may have certain rights regarding your personal information,
                    including:</p>
                <ul>
                    <li>Access to your personal information</li>
                    <li>Correction of inaccurate or incomplete information</li>
                    <li>Deletion of your personal information</li>
                    <li>Restriction of processing of your personal information</li>
                    <li>Data portability</li>
                    <li>Objection to processing of your personal information</li>
                </ul>
                <p>To exercise these rights, please contact us using the information provided in the "Contact Us"
                    section.</p>
            </div>

            <div class="legal-section">
                <h2>7. Children's Privacy</h2>
                <p>Our services are not intended for children under the age of 13. We do not knowingly collect personal
                    information from children under 13. If you are a parent or guardian and believe that your child has
                    provided us with personal information, please contact us.</p>
            </div>

            <div class="legal-section">
                <h2>8. Changes to This Privacy Policy</h2>
                <p>We may update this Privacy Policy from time to time. We will notify you of any changes by posting the
                    new Privacy Policy on this page and updating the "Last updated" date. You are advised to review this
                    Privacy Policy periodically for any changes.</p>
            </div>

            <div class="legal-section content-section">
                <h2>9. Contact Us</h2>
                <p>If you have any questions or concerns about this Privacy Policy, please contact us at:</p>
                <p>Email: <EMAIL></p>
                <!-- <p>Address: [Your Company Address] -->
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script src="js/main.js"></script>
    <script src="js/ai-suggestions.js"></script>
    <script src="js/workflow-automation.js"></script>
    <script src="js/ai-feedback-system.js"></script>
</body>

</html>