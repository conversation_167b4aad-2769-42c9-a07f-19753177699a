<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Analytics Dashboard - Promz</title>
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .analytics-dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .analytics-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .analytics-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #4a6cf7;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        
        .feedback-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 15px 0;
        }
        
        .feedback-item:last-child {
            border-bottom: none;
        }
        
        .feedback-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }
        
        .feedback-content {
            color: #333;
            margin-bottom: 5px;
        }
        
        .feedback-rating {
            display: inline-block;
            background: #4a6cf7;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .export-btn {
            background: #4a6cf7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .export-btn:hover {
            background: #3959d9;
        }
        
        .clear-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .clear-btn:hover {
            background: #d32f2f;
        }
        
        .chart-container {
            height: 200px;
            background: #f8f9fa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="analytics-dashboard">
        <h1>AI Analytics Dashboard</h1>
        <p>Monitor AI suggestions, user behavior, and feedback patterns</p>
        
        <div class="analytics-grid">
            <div class="analytics-card">
                <h3>Total Feedback</h3>
                <div class="metric-value" id="total-feedback">0</div>
                <div class="metric-label">feedback items collected</div>
            </div>
            
            <div class="analytics-card">
                <h3>Average Rating</h3>
                <div class="metric-value" id="average-rating">0</div>
                <div class="metric-label">out of 5 stars</div>
            </div>
            
            <div class="analytics-card">
                <h3>Suggestions Shown</h3>
                <div class="metric-value" id="suggestions-shown">0</div>
                <div class="metric-label">AI suggestions displayed</div>
            </div>
            
            <div class="analytics-card">
                <h3>Workflow Triggers</h3>
                <div class="metric-value" id="workflow-triggers">0</div>
                <div class="metric-label">automation events fired</div>
            </div>
        </div>
        
        <div class="analytics-grid">
            <div class="analytics-card">
                <h3>Feedback by Type</h3>
                <div class="chart-container" id="feedback-type-chart">
                    Chart will be rendered here
                </div>
            </div>
            
            <div class="analytics-card">
                <h3>User Behavior Patterns</h3>
                <div class="chart-container" id="behavior-chart">
                    Chart will be rendered here
                </div>
            </div>
        </div>
        
        <div class="analytics-card">
            <h3>Recent Feedback</h3>
            <div style="margin-bottom: 15px;">
                <button class="export-btn" onclick="exportData()">Export Data</button>
                <button class="export-btn" onclick="exportCSV()">Export CSV</button>
                <button class="clear-btn" onclick="clearData()">Clear All Data</button>
            </div>
            <div id="recent-feedback">
                <!-- Feedback items will be populated here -->
            </div>
        </div>
        
        <div class="analytics-card">
            <h3>Workflow Execution Log</h3>
            <div id="workflow-log">
                <!-- Workflow log will be populated here -->
            </div>
        </div>
    </div>

    <script>
        class AIAnalyticsDashboard {
            constructor() {
                this.init();
            }

            init() {
                this.loadAnalytics();
                this.renderCharts();
                this.setupAutoRefresh();
            }

            loadAnalytics() {
                // Load feedback data
                const feedbackData = JSON.parse(localStorage.getItem('promz_feedback_data') || '[]');
                const feedbackPatterns = JSON.parse(localStorage.getItem('promz_feedback_patterns') || '{}');
                const workflowLog = JSON.parse(localStorage.getItem('promz_workflow_log') || '[]');
                
                // Update metrics
                document.getElementById('total-feedback').textContent = feedbackData.length;
                document.getElementById('average-rating').textContent = 
                    feedbackPatterns.averageRating ? feedbackPatterns.averageRating.toFixed(1) : '0';
                document.getElementById('suggestions-shown').textContent = 
                    this.countSuggestions(feedbackData);
                document.getElementById('workflow-triggers').textContent = workflowLog.length;
                
                // Render recent feedback
                this.renderRecentFeedback(feedbackData);
                
                // Render workflow log
                this.renderWorkflowLog(workflowLog);
            }

            countSuggestions(feedbackData) {
                return feedbackData.filter(item => item.type === 'contextual' || item.type === 'quick').length;
            }

            renderRecentFeedback(feedbackData) {
                const container = document.getElementById('recent-feedback');
                
                if (feedbackData.length === 0) {
                    container.innerHTML = '<p style="color: #666; text-align: center;">No feedback data available</p>';
                    return;
                }
                
                const recentFeedback = feedbackData
                    .sort((a, b) => b.timestamp - a.timestamp)
                    .slice(0, 10);
                
                container.innerHTML = recentFeedback.map(item => `
                    <div class="feedback-item">
                        <div class="feedback-meta">
                            ${new Date(item.timestamp).toLocaleString()} | 
                            Type: ${item.type} | 
                            ${item.category ? `Category: ${item.category}` : ''}
                            ${item.rating ? `<span class="feedback-rating">${item.rating}/5</span>` : ''}
                        </div>
                        <div class="feedback-content">
                            ${item.message || item.feedback || 'Quick feedback'}
                        </div>
                        ${item.metadata && item.metadata.page ? 
                            `<div class="feedback-meta">Page: ${item.metadata.page.url}</div>` : ''}
                    </div>
                `).join('');
            }

            renderWorkflowLog(workflowLog) {
                const container = document.getElementById('workflow-log');
                
                if (workflowLog.length === 0) {
                    container.innerHTML = '<p style="color: #666; text-align: center;">No workflow data available</p>';
                    return;
                }
                
                const recentLog = workflowLog
                    .sort((a, b) => b.timestamp - a.timestamp)
                    .slice(0, 10);
                
                container.innerHTML = recentLog.map(item => `
                    <div class="feedback-item">
                        <div class="feedback-meta">
                            ${new Date(item.timestamp).toLocaleString()} | 
                            Trigger: ${item.triggerId} | 
                            Page: ${item.page}
                        </div>
                        <div class="feedback-content">
                            Behaviors: Time on page: ${Math.round(item.behaviors.timeOnPage / 1000)}s, 
                            Clicks: ${item.behaviors.clickCount}, 
                            Scroll: ${item.behaviors.scrollDepth}%
                        </div>
                    </div>
                `).join('');
            }

            renderCharts() {
                // Simple text-based charts for now
                this.renderFeedbackTypeChart();
                this.renderBehaviorChart();
            }

            renderFeedbackTypeChart() {
                const feedbackData = JSON.parse(localStorage.getItem('promz_feedback_data') || '[]');
                const types = {};
                
                feedbackData.forEach(item => {
                    types[item.type] = (types[item.type] || 0) + 1;
                });
                
                const chart = document.getElementById('feedback-type-chart');
                if (Object.keys(types).length === 0) {
                    chart.innerHTML = 'No data available';
                    return;
                }
                
                chart.innerHTML = Object.entries(types)
                    .map(([type, count]) => `
                        <div style="margin-bottom: 5px;">
                            <strong>${type}:</strong> ${count}
                        </div>
                    `).join('');
            }

            renderBehaviorChart() {
                const workflowLog = JSON.parse(localStorage.getItem('promz_workflow_log') || '[]');
                const triggers = {};
                
                workflowLog.forEach(item => {
                    triggers[item.triggerId] = (triggers[item.triggerId] || 0) + 1;
                });
                
                const chart = document.getElementById('behavior-chart');
                if (Object.keys(triggers).length === 0) {
                    chart.innerHTML = 'No data available';
                    return;
                }
                
                chart.innerHTML = Object.entries(triggers)
                    .map(([trigger, count]) => `
                        <div style="margin-bottom: 5px;">
                            <strong>${trigger}:</strong> ${count}
                        </div>
                    `).join('');
            }

            setupAutoRefresh() {
                // Refresh every 30 seconds
                setInterval(() => {
                    this.loadAnalytics();
                    this.renderCharts();
                }, 30000);
            }
        }

        // Export functions
        function exportData() {
            const data = {
                feedback: JSON.parse(localStorage.getItem('promz_feedback_data') || '[]'),
                patterns: JSON.parse(localStorage.getItem('promz_feedback_patterns') || '{}'),
                workflow: JSON.parse(localStorage.getItem('promz_workflow_log') || '[]'),
                clickHistory: JSON.parse(localStorage.getItem('promz_click_history') || '[]'),
                visitHistory: JSON.parse(localStorage.getItem('promz_visit_history') || '[]')
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `promz-ai-analytics-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function exportCSV() {
            const feedbackData = JSON.parse(localStorage.getItem('promz_feedback_data') || '[]');
            
            if (feedbackData.length === 0) {
                alert('No feedback data to export');
                return;
            }
            
            const headers = ['Timestamp', 'Type', 'Category', 'Rating', 'Message', 'Page'];
            const rows = feedbackData.map(item => [
                new Date(item.timestamp).toISOString(),
                item.type,
                item.category || '',
                item.rating || '',
                (item.message || item.feedback || '').replace(/"/g, '""'),
                item.metadata && item.metadata.page ? item.metadata.page.url : ''
            ]);
            
            const csv = [headers, ...rows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `promz-feedback-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearData() {
            if (confirm('Are you sure you want to clear all analytics data? This cannot be undone.')) {
                localStorage.removeItem('promz_feedback_data');
                localStorage.removeItem('promz_feedback_patterns');
                localStorage.removeItem('promz_workflow_log');
                localStorage.removeItem('promz_click_history');
                localStorage.removeItem('promz_visit_history');
                localStorage.removeItem('promz_ai_feedback');
                localStorage.removeItem('promz_trigger_cooldowns');
                
                alert('All analytics data has been cleared.');
                location.reload();
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new AIAnalyticsDashboard();
        });
    </script>
</body>
</html>
