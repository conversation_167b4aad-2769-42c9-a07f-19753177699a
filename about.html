<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Promz - AI Prompt Management Tool</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/ai-components.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('about.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="about.html" style="color: #4a6cf7; text-decoration: none; font-weight: 500;">About</a>
                                <a href="download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="beta/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>About Promz</h1>
        </div>
    </section>

    <section class="about-content">
        <div class="container">
            <div class="about-section">
                <h2>Our Mission</h2>
                <p>At Promz, we're on a mission to simplify AI prompt management and collaboration. We believe that the
                    power of AI should be accessible to everyone, and well-crafted prompts are the key to unlocking that
                    potential.</p>
                <p>Our platform provides a comprehensive solution for organizing, optimizing, and collaborating on AI
                    prompts, making it easier for individuals and teams to harness the power of AI in their workflows.
                </p>
            </div>

            <div class="about-section">
                <h2>Our Story</h2>
                <p>Promz was founded in 2024 by a team of AI enthusiasts who recognized the growing importance of prompt
                    engineering in the AI ecosystem. As AI language models became more powerful, the need for a
                    dedicated tool to manage and optimize prompts became increasingly apparent.</p>
                <p>What started as a simple tool for personal use has evolved into a comprehensive platform that serves
                    individuals, teams, and organizations across various industries.</p>
            </div>

            <div class="about-section">
                <h2>Our Technology</h2>
                <p>Promz is built on a modern technology stack that ensures reliability, performance, and security:</p>
                <ul>
                    <li><strong>Client Application:</strong> Developed with Flutter for cross-platform compatibility
                    </li>
                    <li><strong>Backend API:</strong> Powered by Go for high performance and reliability</li>
                    <li><strong>Database:</strong> Advanced data storage with real-time synchronization</li>
                    <li><strong>AI Integration:</strong> Seamless connection with popular AI providers</li>
                </ul>
                <p>Our architecture follows the MVVM pattern on the client side, ensuring a clean separation of concerns
                    and maintainable codebase.</p>
            </div>

            <div class="about-section">
                <h2>Our Team</h2>
                <p>Promz is developed by a diverse team of engineers, designers, and AI specialists who are passionate
                    about creating tools that empower users to get the most out of AI technology.</p>
                <p>We're committed to continuous improvement and innovation, regularly incorporating user feedback to
                    enhance the platform and add new features.</p>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="beta/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script src="js/main.js"></script>
    <script src="js/ai-suggestions.js"></script>
    <script src="js/workflow-automation.js"></script>
    <script src="js/ai-feedback-system.js"></script>
</body>

</html>