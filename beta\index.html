<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz Beta Program</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="beta.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically with fallback
        fetch('../includes/header.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the header for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="../feedback/index.html"');

                document.getElementById('header-placeholder').innerHTML = data;

                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                    if (link.href.includes('beta/index.html') || link.textContent.trim() === 'Beta Program') {
                        link.classList.add('active');
                    }
                });
            })
            .catch(error => {
                console.log('Loading fallback header due to:', error);
                // Fallback header
                document.getElementById('header-placeholder').innerHTML = `
                    <header style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-right: 10px;">
                                <a href="../index.html" style="text-decoration: none; font-weight: bold; color: #4a6cf7; font-size: 1.5rem;">Promz</a>
                            </div>
                            <nav style="display: flex; gap: 1.5rem;">
                                <a href="../index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Home</a>
                                <a href="../about.html" style="color: #212529; text-decoration: none; font-weight: 500;">About</a>
                                <a href="../download.html" style="color: #212529; text-decoration: none; font-weight: 500;">Download</a>
                                <a href="index.html" style="color: #4a6cf7; text-decoration: none; font-weight: 500;">Beta Program</a>
                                <a href="../feedback/index.html" style="color: #212529; text-decoration: none; font-weight: 500;">Feedback</a>
                            </nav>
                        </div>
                    </header>
                `;
            });
    </script>

    <script>
        // Load header dynamically
        fetch('../includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('beta/index.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });
    </script>

    <section class="hero">
        <div class="container">
            <h1>Promz Beta Program</h1>
            <p>Be among the first to try new features and help shape the future of Promz</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="beta-container">
                <h2>Join Our Beta Testing Community</h2>
                <p>
                    Thank you for your interest in the Promz Beta Program! As a beta tester, you'll get early access to new features
                    and updates before they're available to the general public. Your feedback is invaluable in helping us improve
                    Promz and deliver the best possible experience to all users.
                </p>

                <h3>Available Beta Platforms</h3>
                <div class="beta-platforms">
                    <div class="beta-platform-card">
                        <img src="../images/android-logo.svg" alt="Android Logo">
                        <h3>Android Beta</h3>
                        <span class="status available">Available Now</span>
                        <p>
                            Our Android beta is currently available through the Google Play Store's testing program.
                            Get early access to new features and help us improve the Android experience.
                        </p>
                        <a href="https://play.google.com/apps/testing/ai.promz" class="btn primary">Install Android Beta</a>
                        <a href="../releases/index.html" class="btn secondary">View Release Notes</a>
                    </div>

                    <div class="beta-platform-card">
                        <img src="../images/apple-logo.svg" alt="iOS Logo">
                        <h3>iOS Beta</h3>
                        <span class="status coming-soon">Coming Soon</span>
                        <p>
                            Our iOS beta program is coming soon! We're working hard to bring Promz to iOS devices
                            and will be launching a TestFlight beta in the near future.
                        </p>
                        <a href="../feedback/index.html" class="btn secondary">Request Early Access</a>
                    </div>
                </div>

                <h3>Why Join the Beta Program?</h3>
                <ul>
                    <li><strong>Early Access:</strong> Be the first to try new features and improvements</li>
                    <li><strong>Direct Influence:</strong> Your feedback directly shapes the development of Promz</li>
                    <li><strong>Community:</strong> Join a community of passionate users helping to improve the app</li>
                    <li><strong>Insider Updates:</strong> Get behind-the-scenes information about upcoming features</li>
                </ul>

                <h3>How to Provide Feedback</h3>
                <p>
                    Your feedback is crucial to the success of our beta program. If you encounter any issues, have suggestions,
                    or want to share your thoughts about the beta, please use our feedback forms:
                </p>
                <ul>
                    <li><a href="../feedback/index.html">General Feedback</a> - Share your thoughts and suggestions</li>
                    <li><a href="../feedback/feature-request.html">Feature Requests</a> - Tell us what you'd like to see in future updates</li>
                    <li><a href="../feedback/bug-report.html">Bug Reports</a> - Report any issues you encounter</li>
                </ul>

                <h3>Interested in Alpha Testing?</h3>
                <p>
                    For those who want to be on the absolute cutting edge, we occasionally offer alpha testing opportunities
                    for very early builds. These versions may be less stable but give you access to features in their earliest stages.
                </p>
                <p>
                    If you're interested in alpha testing, please <a href="../feedback/index.html">contact us</a> through our feedback form
                    and mention your interest in alpha testing. Be sure to include which platforms you use and any relevant experience.
                </p>
                <p>
                    <a href="../feedback/index.html">Contact us</a> through our feedback form
                </p>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically with fallback
        fetch('../includes/footer.html')
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(data => {
                // Fix relative paths in the footer for this subdirectory
                data = data.replace(/src="images\//g, 'src="../images/');
                data = data.replace(/href="index\.html"/g, 'href="../index.html"');
                data = data.replace(/href="about\.html"/g, 'href="../about.html"');
                data = data.replace(/href="download\.html"/g, 'href="../download.html"');
                data = data.replace(/href="beta\/index\.html"/g, 'href="index.html"');
                data = data.replace(/href="feedback\/index\.html"/g, 'href="../feedback/index.html"');
                data = data.replace(/href="terms\.html"/g, 'href="../terms.html"');
                data = data.replace(/href="privacy\.html"/g, 'href="../privacy.html"');

                document.getElementById('footer-placeholder').innerHTML = data;
            })
            .catch(error => {
                console.log('Loading fallback footer due to:', error);
                // Fallback footer
                document.getElementById('footer-placeholder').innerHTML = `
                    <footer style="background-color: #212529; color: white; padding: 3rem 0 2rem;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem;">
                                <div>
                                    <img src="../images/logo.png" alt="Promz Logo" style="height: 40px; margin-bottom: 1rem;">
                                    <p style="opacity: 0.7; font-size: 0.875rem;">&copy; 2025 Promz. All rights reserved.</p>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Links</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Home</a>
                                        <a href="../about.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">About</a>
                                        <a href="../download.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Download</a>
                                        <a href="index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Beta Program</a>
                                        <a href="../feedback/index.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Feedback</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Legal</h3>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <a href="../terms.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Terms of Service</a>
                                        <a href="../privacy.html" style="color: rgba(255,255,255,0.7); text-decoration: none;">Privacy Policy</a>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="color: white; margin-bottom: 1.5rem; font-size: 1.25rem;">Contact Us</h3>
                                    <p style="color: rgba(255,255,255,0.7); margin-bottom: 1rem;">Email: <EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </footer>
                `;
            });
    </script>

    <script>
        // Load footer dynamically
        fetch('../includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>
</body>
</html>