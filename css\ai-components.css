/**
 * AI Components Styling
 * Styles for AI suggestions, workflow automation, and feedback systems
 * Uses Promz Design System for consistency
 */

/* Design System Variables - Fallbacks for consistency */
:root {
    --primary-color: #4a6cf7;
    --primary-dark: #3959d9;
    --heading-color: #262d3f;
    --body-text: #4d5d77;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --border-color: #e5eaef;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;

    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    --font-heading: 'Inter', sans-serif;

    --border-radius: 0.375rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);

    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

/* AI Suggestions Panel */
.ai-suggestions-panel {
    position: fixed;
    top: 20px;
    right: -400px;
    width: 380px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow: hidden;
}

.ai-suggestions-panel.show {
    right: 20px;
}

.ai-suggestions-header {
    background: linear-gradient(135deg, #4a6cf7 0%, #3959d9 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-suggestions-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.ai-suggestions-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s;
}

.ai-suggestions-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-suggestions-content {
    max-height: calc(80vh - 70px);
    overflow-y: auto;
    padding: 0;
}

.ai-suggestion-item {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    gap: 12px;
    transition: background 0.2s;
}

.ai-suggestion-item:hover {
    background: #f8f9fa;
}

.ai-suggestion-item:last-child {
    border-bottom: none;
}

.ai-suggestion-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.ai-suggestion-content {
    flex: 1;
}

.ai-suggestion-content h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.ai-suggestion-content p {
    margin: 0 0 12px 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.ai-suggestion-action {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
}

.ai-suggestion-action:hover {
    background: #3959d9;
}

.ai-suggestion-feedback {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
}

.ai-feedback-btn {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.ai-feedback-btn:hover {
    background: #f5f5f5;
}

/* Workflow Automation Components */
.workflow-tooltip {
    position: fixed;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px;
    max-width: 300px;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.workflow-tooltip.tooltip-bottom-right {
    bottom: 20px;
    right: 20px;
}

.workflow-tooltip.tooltip-bottom-left {
    bottom: 20px;
    left: 20px;
}

.workflow-tooltip.tooltip-top-center {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.tooltip-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #333;
}

.tooltip-content p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.tooltip-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tooltip-btn {
    padding: 8px 16px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.tooltip-btn.primary {
    background: #4a6cf7;
    color: white;
    border-color: #4a6cf7;
}

.tooltip-btn:hover {
    background: #f5f5f5;
}

.tooltip-btn.primary:hover {
    background: #3959d9;
}

/* Workflow Modal */
.workflow-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1002;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

.modal-content h3 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
}

.modal-content p {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 10px 20px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.modal-btn.primary {
    background: #4a6cf7;
    color: white;
    border-color: #4a6cf7;
}

.modal-btn:hover {
    background: #f5f5f5;
}

.modal-btn.primary:hover {
    background: #3959d9;
}

/* Workflow Notification */
.workflow-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    max-width: 350px;
    z-index: 1001;
    animation: slideInRight 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-content {
    flex: 1;
}

.notification-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #333;
}

.notification-content p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.notification-action {
    color: #4a6cf7;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

.notification-action:hover {
    text-decoration: underline;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #666;
}

/* AI Feedback System */
.ai-feedback-float {
    position: fixed;
    bottom: var(--spacing-lg, 2rem);
    right: var(--spacing-lg, 2rem);
    background: linear-gradient(135deg, var(--primary-color, #4a6cf7) 0%, var(--primary-dark, #3959d9) 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-sm, 1rem) var(--spacing-lg, 2rem);
    cursor: pointer;
    box-shadow: var(--box-shadow, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
    z-index: 999;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 600;
}

.ai-feedback-float:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(74, 108, 247, 0.4);
}

.feedback-button-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 0.5rem);
}

.feedback-icon {
    font-size: 18px;
}

.feedback-text {
    font-size: 14px;
    font-weight: 600;
}

/* Contextual Feedback Prompts */
.contextual-feedback-prompt {
    margin-top: var(--spacing-md, 1.5rem);
    padding: var(--spacing-sm, 1rem);
    background: var(--light-color, #f8f9fa);
    border-radius: var(--border-radius, 0.375rem);
    border-left: 4px solid var(--primary-color, #4a6cf7);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    font-family: var(--font-primary, 'Inter', sans-serif);
}

/* Show feedback prompt on parent hover */
.content-section:hover .contextual-feedback-prompt,
.feature-card:hover .contextual-feedback-prompt,
.beta-platform-card:hover .contextual-feedback-prompt,
.legal-section:hover .contextual-feedback-prompt {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* Add subtle hover indicator to content sections */
.content-section,
.feature-card,
.beta-platform-card,
.legal-section {
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;
}

/* Add a subtle feedback indicator */
.content-section::after,
.feature-card::after,
.beta-platform-card::after,
.legal-section::after {
    content: "💬";
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px;
    opacity: 0.3;
    transition: opacity 0.2s ease;
    pointer-events: none;
}

.content-section:hover,
.feature-card:hover,
.beta-platform-card:hover,
.legal-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.content-section:hover::after,
.feature-card:hover::after,
.beta-platform-card:hover::after,
.legal-section:hover::after {
    opacity: 0.7;
}

.feedback-prompt-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.feedback-prompt-content span {
    font-size: 14px;
    color: var(--body-text, #4d5d77);
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 500;
}

.feedback-quick-actions {
    display: flex;
    gap: var(--spacing-xs, 0.5rem);
}

.feedback-quick-btn {
    padding: var(--spacing-xs, 0.5rem) var(--spacing-sm, 1rem);
    border: 1px solid var(--border-color, #e5eaef);
    background: white;
    border-radius: var(--border-radius, 0.375rem);
    cursor: pointer;
    font-size: 12px;
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 500;
    color: var(--body-text, #4d5d77);
    transition: all 0.3s ease;
}

.feedback-quick-btn:hover {
    background: var(--light-color, #f8f9fa);
    transform: translateY(-1px);
}

.feedback-quick-btn.positive:hover {
    background: var(--success-color, #28a745);
    border-color: var(--success-color, #28a745);
    color: white;
}

.feedback-quick-btn.negative:hover {
    background: var(--danger-color, #dc3545);
    border-color: var(--danger-color, #dc3545);
    color: white;
}

/* Quick Feedback Tooltip */
.quick-feedback-tooltip {
    position: fixed;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 12px;
    z-index: 1001;
    animation: fadeIn 0.3s ease;
}

.quick-feedback-tooltip .tooltip-content p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #666;
}

.quick-feedback-buttons {
    display: flex;
    gap: 4px;
}

.quick-feedback-btn {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
}

.quick-feedback-btn:hover {
    background: #f5f5f5;
    transform: scale(1.1);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 20px rgba(74, 108, 247, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(74, 108, 247, 0.5);
    }
    100% {
        box-shadow: 0 4px 20px rgba(74, 108, 247, 0.3);
    }
}

/* AI Feedback Modal - Using Promz Design System */
.ai-feedback-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-primary, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif);
}

.feedback-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(33, 37, 41, 0.6); /* Using --dark-color with opacity */
}

.feedback-modal-content {
    background: white;
    border-radius: var(--border-radius, 0.375rem);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
    box-shadow: var(--box-shadow, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
}

.feedback-modal-header {
    padding: var(--spacing-lg, 2rem) var(--spacing-lg, 2rem);
    border-bottom: 1px solid var(--border-color, #e5eaef);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-color, #f8f9fa);
    border-radius: var(--border-radius, 0.375rem) var(--border-radius, 0.375rem) 0 0;
}

.feedback-modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--heading-color, #262d3f);
    font-family: var(--font-heading, 'Inter', sans-serif);
    font-weight: 700;
}

.feedback-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--secondary-color, #6c757d);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.feedback-modal-close:hover {
    background: var(--border-color, #e5eaef);
    color: var(--heading-color, #262d3f);
}

.feedback-modal-body {
    padding: var(--spacing-lg, 2rem);
    background: white;
}

.feedback-category-selection {
    display: block !important;
    visibility: visible !important;
}

.feedback-category-selection h4 {
    margin: 0 0 var(--spacing-md, 1.5rem) 0;
    font-size: 1.25rem;
    color: var(--heading-color, #262d3f);
    text-align: center;
    font-family: var(--font-heading, 'Inter', sans-serif);
    font-weight: 600;
}

.feedback-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-sm, 1rem);
    margin-top: var(--spacing-md, 1.5rem);
}

.feedback-category-btn {
    padding: var(--spacing-md, 1.5rem) var(--spacing-sm, 1rem);
    border: 2px solid var(--border-color, #e5eaef);
    background: white;
    border-radius: var(--border-radius, 0.375rem);
    cursor: pointer;
    font-size: 14px;
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 500;
    color: var(--body-text, #4d5d77);
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs, 0.5rem);
    min-height: 80px;
    justify-content: center;
}

.feedback-category-btn:hover {
    border-color: var(--primary-color, #4a6cf7);
    background: var(--light-color, #f8f9fa);
    color: var(--primary-color, #4a6cf7);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
}

.feedback-category-btn:active {
    transform: translateY(0);
}

.feedback-form h4 {
    margin: var(--spacing-lg, 2rem) 0 var(--spacing-sm, 1rem) 0;
    font-size: 1.125rem;
    color: var(--heading-color, #262d3f);
    font-family: var(--font-heading, 'Inter', sans-serif);
    font-weight: 600;
}

.star-rating {
    display: flex;
    gap: var(--spacing-xs, 0.5rem);
    margin-top: var(--spacing-sm, 1rem);
}

.star {
    font-size: 24px;
    cursor: pointer;
    opacity: 0.3;
    transition: opacity 0.2s;
}

.star:hover,
.star.active {
    opacity: 1;
}

.feedback-text textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-sm, 1rem);
    border: 1px solid var(--border-color, #e5eaef);
    border-radius: var(--border-radius, 0.375rem);
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-size: 14px;
    color: var(--body-text, #4d5d77);
    resize: vertical;
    margin-top: var(--spacing-sm, 1rem);
    transition: border-color 0.2s;
}

.feedback-text textarea:focus {
    outline: none;
    border-color: var(--primary-color, #4a6cf7);
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.feedback-context {
    margin-top: var(--spacing-lg, 2rem);
}

.feedback-context label {
    display: block;
    margin-bottom: var(--spacing-sm, 1rem);
    font-size: 14px;
    color: var(--body-text, #4d5d77);
    font-family: var(--font-primary, 'Inter', sans-serif);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.feedback-context input[type="checkbox"] {
    margin-right: var(--spacing-sm, 1rem);
    accent-color: var(--primary-color, #4a6cf7);
}

.feedback-actions {
    display: flex;
    gap: var(--spacing-sm, 1rem);
    justify-content: flex-end;
    margin-top: var(--spacing-lg, 2rem);
    padding-top: var(--spacing-md, 1.5rem);
    border-top: 1px solid var(--border-color, #e5eaef);
}

.feedback-submit-btn,
.feedback-cancel-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border-color, #e5eaef);
    border-radius: var(--border-radius, 0.375rem);
    cursor: pointer;
    font-size: 14px;
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 600;
    transition: all 0.3s ease;
}

.feedback-submit-btn {
    background: var(--primary-color, #4a6cf7);
    color: white;
    border-color: var(--primary-color, #4a6cf7);
}

.feedback-cancel-btn {
    background: white;
    color: var(--body-text, #4d5d77);
}

.feedback-submit-btn:hover {
    background: var(--primary-dark, #3959d9);
    border-color: var(--primary-dark, #3959d9);
    transform: translateY(-2px);
}

.feedback-cancel-btn:hover {
    background: var(--light-color, #f8f9fa);
    transform: translateY(-2px);
}

.feedback-thank-you {
    text-align: center;
    padding: var(--spacing-xl, 3rem) var(--spacing-lg, 2rem);
}

.feedback-thank-you h3 {
    margin: 0 0 var(--spacing-md, 1.5rem) 0;
    font-size: 1.5rem;
    color: var(--heading-color, #262d3f);
    font-family: var(--font-heading, 'Inter', sans-serif);
    font-weight: 700;
}

.feedback-thank-you p {
    margin: 0 0 var(--spacing-sm, 1rem) 0;
    font-size: 16px;
    color: var(--body-text, #4d5d77);
    font-family: var(--font-primary, 'Inter', sans-serif);
    line-height: 1.6;
}

.feedback-close-btn {
    background: var(--primary-color, #4a6cf7);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius, 0.375rem);
    cursor: pointer;
    font-size: 14px;
    font-family: var(--font-primary, 'Inter', sans-serif);
    font-weight: 600;
    margin-top: var(--spacing-lg, 2rem);
    transition: all 0.3s ease;
}

.feedback-close-btn:hover {
    background: var(--primary-dark, #3959d9);
    transform: translateY(-2px);
}

/* Help Modal */
.ai-help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1004;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-help-modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 90%;
    animation: modalSlideIn 0.3s ease;
}

.ai-help-modal-content h3 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
}

.ai-help-modal-content p {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
}

.ai-help-modal-content ul {
    margin: 0 0 20px 0;
    padding-left: 20px;
}

.ai-help-modal-content li {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.ai-help-modal-content button {
    background: #4a6cf7;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.ai-help-modal-content button:hover {
    background: #3959d9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-suggestions-panel {
        width: calc(100% - 40px);
        right: -100%;
    }

    .ai-suggestions-panel.show {
        right: 20px;
    }

    .workflow-tooltip {
        max-width: calc(100% - 40px);
    }

    .modal-content,
    .feedback-modal-content,
    .ai-help-modal-content {
        width: calc(100% - 40px);
        padding: 20px;
    }

    .workflow-notification {
        width: calc(100% - 40px);
        right: 20px;
    }

    .feedback-categories {
        grid-template-columns: 1fr;
    }

    .ai-feedback-float {
        bottom: 80px;
        right: 20px;
    }
}
