/**
 * Automated Workflow Triggers Based on User Behavior
 * Monitors user events and triggers contextual UI elements
 */

class WorkflowAutomation {
    constructor() {
        this.behaviors = {
            idleTime: 0,
            scrollDepth: 0,
            clickCount: 0,
            formInteractions: 0,
            timeOnPage: 0,
            exitIntent: false
        };
        
        this.triggers = [];
        this.activeWorkflows = new Set();
        this.init();
    }

    init() {
        this.setupBehaviorTracking();
        this.initializeWorkflowTriggers();
        this.startMonitoring();
    }

    setupBehaviorTracking() {
        let idleTimer;
        let lastActivity = Date.now();

        // Track idle time
        const resetIdleTimer = () => {
            lastActivity = Date.now();
            this.behaviors.idleTime = 0;
            clearTimeout(idleTimer);
            
            idleTimer = setTimeout(() => {
                this.behaviors.idleTime = Date.now() - lastActivity;
                this.checkTriggers('idle');
            }, 60000); // 60 seconds
        };

        // Track user activity
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetIdleTimer, true);
        });

        // Track scroll depth
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            this.behaviors.scrollDepth = Math.round((scrollTop / docHeight) * 100);
            
            if (this.behaviors.scrollDepth > 75) {
                this.checkTriggers('deep_scroll');
            }
        });

        // Track clicks
        document.addEventListener('click', (e) => {
            this.behaviors.clickCount++;
            this.trackClickContext(e);
            this.checkTriggers('click');
        });

        // Track form interactions
        document.addEventListener('input', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                this.behaviors.formInteractions++;
                this.checkTriggers('form_interaction');
            }
        });

        // Track exit intent
        document.addEventListener('mouseleave', (e) => {
            if (e.clientY <= 0) {
                this.behaviors.exitIntent = true;
                this.checkTriggers('exit_intent');
            }
        });

        // Track time on page
        setInterval(() => {
            this.behaviors.timeOnPage += 1000;
            this.checkTriggers('time_based');
        }, 1000);
    }

    initializeWorkflowTriggers() {
        this.triggers = [
            // Idle user assistance
            {
                id: 'idle_help',
                condition: () => this.behaviors.idleTime >= 60000,
                action: () => this.showIdleHelpTooltip(),
                triggerType: 'idle',
                cooldown: 300000, // 5 minutes
                priority: 'medium'
            },

            // Deep scroll engagement
            {
                id: 'scroll_engagement',
                condition: () => this.behaviors.scrollDepth >= 80,
                action: () => this.showScrollEngagementModal(),
                triggerType: 'deep_scroll',
                cooldown: 600000, // 10 minutes
                priority: 'low'
            },

            // Form abandonment prevention
            {
                id: 'form_abandonment',
                condition: () => this.behaviors.formInteractions > 0 && this.behaviors.idleTime >= 30000,
                action: () => this.showFormAbandonmentPrompt(),
                triggerType: 'idle',
                cooldown: 180000, // 3 minutes
                priority: 'high'
            },

            // Exit intent retention
            {
                id: 'exit_intent_retention',
                condition: () => this.behaviors.exitIntent && this.behaviors.timeOnPage > 30000,
                action: () => this.showExitIntentModal(),
                triggerType: 'exit_intent',
                cooldown: 86400000, // 24 hours
                priority: 'high'
            },

            // New user onboarding
            {
                id: 'new_user_onboarding',
                condition: () => this.isNewUser() && this.behaviors.timeOnPage > 10000,
                action: () => this.showOnboardingTour(),
                triggerType: 'time_based',
                cooldown: 86400000, // 24 hours
                priority: 'high'
            },

            // Feature discovery
            {
                id: 'feature_discovery',
                condition: () => this.behaviors.clickCount >= 5 && !this.hasVisitedBeta(),
                action: () => this.showFeatureDiscovery(),
                triggerType: 'click',
                cooldown: 3600000, // 1 hour
                priority: 'medium'
            },

            // Feedback encouragement
            {
                id: 'feedback_encouragement',
                condition: () => this.behaviors.timeOnPage > 120000 && !this.hasGivenFeedback(),
                action: () => this.showFeedbackEncouragement(),
                triggerType: 'time_based',
                cooldown: 86400000, // 24 hours
                priority: 'low'
            }
        ];
    }

    checkTriggers(triggerType) {
        this.triggers
            .filter(trigger => trigger.triggerType === triggerType)
            .filter(trigger => !this.isOnCooldown(trigger.id))
            .filter(trigger => trigger.condition())
            .sort((a, b) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority))
            .slice(0, 1) // Only trigger highest priority
            .forEach(trigger => {
                this.executeTrigger(trigger);
            });
    }

    executeTrigger(trigger) {
        if (this.activeWorkflows.has(trigger.id)) return;

        this.activeWorkflows.add(trigger.id);
        this.setCooldown(trigger.id, trigger.cooldown);
        
        try {
            trigger.action();
            this.logTriggerExecution(trigger);
        } catch (error) {
            console.error('Workflow trigger error:', error);
        }
    }

    // Workflow Actions
    showIdleHelpTooltip() {
        const tooltip = this.createTooltip({
            title: '👋 Still here?',
            content: 'Need help finding something? Check out our features or get support.',
            actions: [
                { text: 'Explore Features', action: () => window.location.href = 'about.html' },
                { text: 'Get Help', action: () => window.location.href = 'feedback/index.html' },
                { text: 'Dismiss', action: () => this.dismissWorkflow('idle_help') }
            ],
            position: 'bottom-right',
            autoHide: 15000,
            workflowId: 'idle_help'
        });
    }

    showScrollEngagementModal() {
        const modal = this.createModal({
            title: '🎉 You\'re engaged!',
            content: 'Since you\'re exploring our content, would you like to join our beta program for early access to new features?',
            actions: [
                { text: 'Join Beta Program', action: () => window.location.href = 'beta/index.html', primary: true },
                { text: 'Maybe Later', action: () => this.dismissWorkflow('scroll_engagement') }
            ],
            workflowId: 'scroll_engagement'
        });
    }

    showFormAbandonmentPrompt() {
        const prompt = this.createTooltip({
            title: '💭 Need assistance?',
            content: 'Having trouble with the form? We\'re here to help!',
            actions: [
                { text: 'Get Help', action: () => this.showFormHelp() },
                { text: 'Continue', action: () => this.dismissWorkflow('form_abandonment') }
            ],
            position: 'top-center',
            autoHide: 10000,
            workflowId: 'form_abandonment'
        });
    }

    showExitIntentModal() {
        const modal = this.createModal({
            title: '👋 Wait! Before you go...',
            content: 'Don\'t miss out on our latest AI features. Join our newsletter or try our beta program!',
            actions: [
                { text: 'Try Beta', action: () => window.location.href = 'beta/index.html', primary: true },
                { text: 'Download App', action: () => window.location.href = 'download.html' },
                { text: 'Close', action: () => this.dismissWorkflow('exit_intent_retention') }
            ],
            workflowId: 'exit_intent_retention'
        });
    }

    showOnboardingTour() {
        const tour = this.createOnboardingTour([
            {
                target: 'nav',
                title: 'Welcome to Promz!',
                content: 'Navigate through our features using this menu.',
                position: 'bottom'
            },
            {
                target: '.btn.primary',
                title: 'Get Started',
                content: 'Click here to download our app or explore features.',
                position: 'top'
            },
            {
                target: 'footer',
                title: 'Need Help?',
                content: 'Find support links and contact information in the footer.',
                position: 'top'
            }
        ]);
    }

    showFeatureDiscovery() {
        const notification = this.createNotification({
            title: '🚀 Discover More',
            content: 'You seem interested in our features! Check out our beta program for exclusive access.',
            action: { text: 'Learn More', url: 'beta/index.html' },
            duration: 8000
        });
    }

    showFeedbackEncouragement() {
        const tooltip = this.createTooltip({
            title: '💬 Share Your Thoughts',
            content: 'Your feedback helps us improve. Share your experience with us!',
            actions: [
                { text: 'Give Feedback', action: () => window.location.href = 'feedback/index.html' },
                { text: 'Not Now', action: () => this.dismissWorkflow('feedback_encouragement') }
            ],
            position: 'bottom-left',
            autoHide: 12000,
            workflowId: 'feedback_encouragement'
        });
    }

    // UI Creation Methods
    createTooltip({ title, content, actions, position, autoHide, workflowId }) {
        const tooltip = document.createElement('div');
        tooltip.className = `workflow-tooltip tooltip-${position}`;
        if (workflowId) {
            tooltip.dataset.workflowId = workflowId;
        }

        tooltip.innerHTML = `
            <div class="tooltip-content">
                <h4>${title}</h4>
                <p>${content}</p>
                <div class="tooltip-actions">
                    ${actions.map((action, index) => `
                        <button class="tooltip-btn ${action.primary ? 'primary' : ''}"
                                data-action-index="${index}">${action.text}</button>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(tooltip);

        // Bind action events
        actions.forEach((action, index) => {
            const button = tooltip.querySelector(`[data-action-index="${index}"]`);
            if (button) {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    try {
                        action.action();
                    } catch (error) {
                        console.error('Tooltip action error:', error);
                    }
                    // Remove tooltip after action
                    tooltip.remove();
                });
            }
        });

        if (autoHide) {
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, autoHide);
        }

        return tooltip;
    }

    createModal({ title, content, actions, workflowId }) {
        const modal = document.createElement('div');
        modal.className = 'workflow-modal';
        if (workflowId) {
            modal.dataset.workflowId = workflowId;
        }

        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <h3>${title}</h3>
                <p>${content}</p>
                <div class="modal-actions">
                    ${actions.map((action, index) => `
                        <button class="modal-btn ${action.primary ? 'primary' : ''}"
                                data-action-index="${index}">${action.text}</button>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Bind action events
        actions.forEach((action, index) => {
            const button = modal.querySelector(`[data-action-index="${index}"]`);
            if (button) {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    try {
                        action.action();
                    } catch (error) {
                        console.error('Modal action error:', error);
                    }
                    // Remove modal after action
                    modal.remove();
                });
            }
        });

        // Close on overlay click
        const overlay = modal.querySelector('.modal-overlay');
        if (overlay) {
            overlay.addEventListener('click', () => {
                if (workflowId) {
                    this.dismissWorkflow(workflowId);
                }
                modal.remove();
            });
        }

        return modal;
    }

    createNotification({ title, content, action, duration }) {
        const notification = document.createElement('div');
        notification.className = 'workflow-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <h4>${title}</h4>
                <p>${content}</p>
                ${action ? `<a href="${action.url}" class="notification-action">${action.text}</a>` : ''}
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">&times;</button>
        `;

        document.body.appendChild(notification);
        
        if (duration) {
            setTimeout(() => notification.remove(), duration);
        }

        return notification;
    }

    showFormHelp() {
        // Create and show help modal
        const modal = document.createElement('div');
        modal.className = 'ai-help-modal';
        modal.innerHTML = `
            <div class="ai-help-modal-content">
                <h3>Form Help</h3>
                <p>Here are some tips for filling out the form:</p>
                <ul>
                    <li>All required fields are marked with *</li>
                    <li>Use clear, descriptive language</li>
                    <li>Check your email address for accuracy</li>
                </ul>
                <button onclick="this.closest('.ai-help-modal').remove()">Close</button>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Utility Methods
    trackClickContext(event) {
        const context = {
            element: event.target.tagName,
            className: event.target.className,
            id: event.target.id,
            timestamp: Date.now()
        };
        
        // Store click context for analysis
        const clickHistory = JSON.parse(localStorage.getItem('promz_click_history') || '[]');
        clickHistory.push(context);
        
        // Keep only last 50 clicks
        if (clickHistory.length > 50) {
            clickHistory.shift();
        }
        
        localStorage.setItem('promz_click_history', JSON.stringify(clickHistory));
    }

    isNewUser() {
        return !localStorage.getItem('promz_returning_user');
    }

    hasVisitedBeta() {
        const history = JSON.parse(localStorage.getItem('promz_visit_history') || '[]');
        return history.some(visit => visit.path.includes('beta'));
    }

    hasGivenFeedback() {
        return localStorage.getItem('promz_ai_feedback') !== null;
    }

    isOnCooldown(triggerId) {
        const cooldowns = JSON.parse(localStorage.getItem('promz_trigger_cooldowns') || '{}');
        const lastTriggered = cooldowns[triggerId];
        return lastTriggered && (Date.now() - lastTriggered) < this.getTriggerCooldown(triggerId);
    }

    setCooldown(triggerId, duration) {
        const cooldowns = JSON.parse(localStorage.getItem('promz_trigger_cooldowns') || '{}');
        cooldowns[triggerId] = Date.now();
        localStorage.setItem('promz_trigger_cooldowns', JSON.stringify(cooldowns));
    }

    getTriggerCooldown(triggerId) {
        const trigger = this.triggers.find(t => t.id === triggerId);
        return trigger ? trigger.cooldown : 3600000; // Default 1 hour
    }

    getPriorityValue(priority) {
        const values = { high: 3, medium: 2, low: 1 };
        return values[priority] || 1;
    }

    dismissWorkflow(workflowId) {
        this.activeWorkflows.delete(workflowId);

        // Remove all elements associated with this workflow
        document.querySelectorAll(`.workflow-tooltip, .workflow-modal, .workflow-notification`).forEach(el => {
            if (el.dataset.workflowId === workflowId || el.classList.contains('workflow-modal')) {
                el.remove();
            }
        });

        // Also remove any modals that might not have the dataset
        document.querySelectorAll('.workflow-modal').forEach(modal => {
            if (modal.dataset.workflowId === workflowId) {
                modal.remove();
            }
        });

        console.log(`Dismissed workflow: ${workflowId}`);
    }

    logTriggerExecution(trigger) {
        const log = {
            triggerId: trigger.id,
            timestamp: Date.now(),
            page: window.location.pathname,
            behaviors: { ...this.behaviors }
        };

        const executionLog = JSON.parse(localStorage.getItem('promz_workflow_log') || '[]');
        executionLog.push(log);
        
        // Keep only last 100 executions
        if (executionLog.length > 100) {
            executionLog.shift();
        }
        
        localStorage.setItem('promz_workflow_log', JSON.stringify(executionLog));
    }

    startMonitoring() {
        console.log('Workflow automation started');
        
        // Mark user as returning user after 30 seconds
        setTimeout(() => {
            localStorage.setItem('promz_returning_user', 'true');
        }, 30000);
    }
}

// Initialize Workflow Automation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.workflowAutomation = new WorkflowAutomation();
});
